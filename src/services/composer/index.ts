import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import {
  InternalLocalMessage,
  isHumanMessage,
  LocalMessage,
  SayTool,
  FilePersistedStateType,
  InternalLocalMessage_Tool_EditFile,
  IndeterminatedWorkingSetEffectState,
  FileIndeterminateStateType,
  WebviewMessage,
  ExecuteCommandResponse,
  InternalWebviewMessage,
  DiffSet,
  DiffContent,
} from "shared/lib/agent";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { Bridge } from "@bridge";
import { LocalService as KwaipilotBinaryModule } from "../../core/localService";
import { ComposerSessionStorageService, isPersistedToolEditFileMessage, PersistedComposerSessionData } from "./ComposerSessionStorageService";
import { isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { produce } from "immer";
import { ApiConversationHistoryStorageService } from "./ApiConversationHistoryStorageService";
import { ToIdeFromCoreProtocol } from "shared/lib/LocalService";
import AsyncLock from "async-lock";
import * as vscode from "vscode";
import * as path from "path";
import os from "os";
import pWaitFor from "p-wait-for";
import delay from "delay";
import { listFiles } from "./glob/list-files";
import { ComposerHistoryStorageService } from "./ComposerHistoryStorageService";
import { BlockCodeCacheStorageService } from "./BlockCodeCacheStorageService";
import { SerializedEditorState } from "lexical";
import { TerminalManager } from "../terminal";
import { toPosixPath } from "shared/lib/agent/toPosixPath";
import { formatFilesList } from "./formatFilesList";
import { truncateToSessionName } from "./truncateToSessionName";
import { arePathsEqual } from "../../utils/path";
import { editorStateToContextualTextForLlm } from "./editorStateToContextualTextForLlm";
import { WriteToFileService } from "../write-to-file";
import { findLast, uniq } from "lodash";
import { LoggerManager } from "../../base/logger";
import { ExtensionComposerShape, WebviewContext } from "shared/lib/bridge/protocol";
import { ConfigManager, GlobalStateManager, WorkspaceStateManager } from "../../base/state-manager";
import { Config, GlobalState, WorkspaceState } from "shared/lib/state-manager/types";
import { buildDeviceLog } from "../../log/Model";
import pkg from "../../../package.json";
import curProjectInfos from "../../utils/projectInfo";
import { SupportedModelEnum, SupportedModels } from "shared/lib/agent/supportedModels";
import { WebloggerManager } from "../../base/weblogger";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { Uri, TextDocument } from "vscode";
import { Webview } from "@webview";
import { IndexFileService } from "../index-file";
import { generateCustomUUID } from "shared/lib/misc/generateCustomUUID";
import { toRangeData } from "shared";
import { diffLines } from "diff";
import { extractTextFromFile } from "./extract-text";
import { RulesService } from "../rules";
import { SerializedDiagnostic } from "shared/lib/misc/diagnostic";
import { fromSerializedDiagnostic } from "./fromSerializedDiagnostic";
import { KwaipilotEnv } from "../../const";
import { NonPersistentSessionState, SessionState } from "./SessionState";
import { createLocalMessageMapper } from "./localMessageMapper";

const cwd = vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0) ?? path.join(os.homedir(), "Desktop"); // may or may not exist but fs checking existence would immediately ask for permission which would be bad UX, need to come up with a better solution

const LOCK_KEY_GET_LOCAL_MESSAGES = "getLocalMessages";

export class ComposerService extends ServiceModule implements ExtensionComposerShape {
  private loggerScope = "ComposerService";
  terminalManager = new TerminalManager();

  _sessionState = new SessionState();

  get sessionState() {
    return this._sessionState.state;
  }

  set sessionState(state: NonPersistentSessionState) {
    this._sessionState.state = state;
  }

  /**
   * 当前对话的模型
   */
  userPreferredModel: SupportedModels = SupportedModelEnum.claude3;

  lock = new AsyncLock();

  constructor(ext: ContextManager) {
    super(ext);
    this.setupWebviewMessageListener();
    this.setupLocalServiceMessageListener();
    this.setupEditorFileActionListener();
    this.userPreferredModel = this.getBase(GlobalStateManager).get(GlobalState.COMPOSER_PREFERRED_MODEL, SupportedModelEnum.claude3);
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.addToComposerContext", () => {
        this.handleAddToComposerContext();
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.addFileToContext", (uri?: vscode.Uri) => {
        this.handleAddFileToContext(uri);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.addTerminalToComposerContext", (args: { selection: string; selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } } }) => {
        this.handleAddTerminalToComposerContext(args.selection, args.selectionRange);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.addEditorSelectionToComposerContext", (args: { selection: string; selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } }; uri: string; relativePath: string }) => {
        this.handleAddEditorSelectionToComposerContext(args.selection, args.selectionRange, args.uri, args.relativePath);
      }),
    );
  }

  $getWorkspaceFile(): string | undefined {
    return vscode.workspace.workspaceFile?.toString();
  }

  $openDiagnosticSetting(): void {
    vscode.commands.executeCommand("kwaipilot.openFunctionManagement");
  }

  async handleAddToComposerContext() {
    if (!KwaipilotEnv.isInIde) {
    // ide 会很奇怪唤起terminal
      await this.getBase(Webview).focus("key_binding");
    }

    const textEditor = vscode.window.activeTextEditor;
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    if (!textEditor) {
      webviewBridge.$addToComposerContext(null);
    }
    else {
      const { document, selection } = textEditor;
      // 把当前 selection 扩展到一行
      const spanedRange = new vscode.Range(
        new vscode.Position(selection.start.line, 0),
        new vscode.Position(selection.end.line, document.lineAt(selection.end.line).range.end.character),
      );
      const selectionContent = document.getText(spanedRange);
      if (!selectionContent) {
        webviewBridge.$addToComposerContext(null);
        return;
      }
      webviewBridge.$addToComposerContext({
        type: "selection",
        uri: document.uri.toString(),
        content: selectionContent,
        range: toRangeData(spanedRange),
        relativePath: vscode.workspace.asRelativePath(document.uri.fsPath),
      });
    }
  }

  /**
   * 处理将文件添加到智能体上下文的逻辑
   * 支持从文件资源管理器、编辑器标题等多个场景调用
   * @param uri 文件的 URI，如果未提供则使用当前活动编辑器的文件
   */
  async handleAddFileToContext(uri?: vscode.Uri) {
    // 首先聚焦到智能体 webview
    await this.getBase(Webview).focus("key_binding");

    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);

    // 确定要添加的文件 URI
    let targetUri: vscode.Uri | undefined = uri;

    // 如果没有提供 URI，尝试从当前活动编辑器获取
    if (!targetUri) {
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor) {
        targetUri = activeEditor.document.uri;
      }
    }

    // 如果仍然没有找到文件，则只聚焦到智能体页面
    if (!targetUri) {
      this.logger.warn("handleAddFileToContext: 未找到要添加的文件", this.loggerScope);
      webviewBridge.$addToComposerContext(null);
      return;
    }

    try {
      // 检查文件是否存在且可读
      const stat = await vscode.workspace.fs.stat(targetUri);

      if (stat.type !== vscode.FileType.File && stat.type !== vscode.FileType.Directory) {
        // 检查是否为文件（而非目录）
        this.logger.warn(`handleAddFileToContext: ${targetUri.toString()} 不是一个文件/目录`, this.loggerScope);
        vscode.window.showWarningMessage(`${vscode.workspace.asRelativePath(targetUri.fsPath)} 不是一个文件/目录`);
        webviewBridge.$addToComposerContext(null);
        return;
      }

      // 构造文件上下文节点
      const fileContextNode: MentionNodeV2Structure = {
        type: stat.type === vscode.FileType.File ? "file" : "tree",
        uri: targetUri.toString(),
        relativePath: vscode.workspace.asRelativePath(targetUri.fsPath),
      };

      // 添加到智能体上下文
      webviewBridge.$addToComposerContext(fileContextNode);

      this.logger.info("handleAddFileToContext: 成功添加文件/目录到智能体上下文", this.loggerScope, {
        value: {
          uri: targetUri.toString(),
          relativePath: fileContextNode.relativePath,
        },
      });
    }
    catch (error) {
      this.logger.error(`handleAddFileToContext: 添加文件/目录失败 ${targetUri.toString()}`, this.loggerScope, {
        err: error,
      });
      vscode.window.showErrorMessage(`无法添加文件/目录到智能体上下文: ${vscode.workspace.asRelativePath(targetUri.fsPath)}`);
      webviewBridge.$addToComposerContext(null);
    }
  }

  /**
   * 处理终端选中内容添加到智能体上下文
   * @param selection 终端选中的文本
   * @param selectionRange 终端选区，x/y 需转换为 line/character
   */
  async handleAddTerminalToComposerContext(selection: string, selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } }) {
    await this.getBase(Webview).focus("key_binding");
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    if (!selection) {
      webviewBridge.$addToComposerContext(null);
      return;
    }

    const range = {
      start: { line: selectionRange.start.y, character: selectionRange.start.x },
      end: { line: selectionRange.end.y, character: selectionRange.end.x },
    };

    webviewBridge.$addToComposerContext({
      type: "selection",
      uri: "terminal://current", // 可自定义标识来源
      content: selection,
      range,
      relativePath: "Lines",
    });
  }

  /**
   * 处理编辑器选中内容添加到智能体上下文
   * @param selection 编辑器选中的文本
   * @param selectionRange 编辑器选区，x/y 需转换为 line/character
   */
  async handleAddEditorSelectionToComposerContext(selection: string, selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } }, uri: string, relativePath: string) {
    await this.getBase(Webview).focus("key_binding");
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    if (!selection) {
      webviewBridge.$addToComposerContext(null);
      return;
    }

    const range = {
      start: { line: selectionRange.start.y, character: selectionRange.start.x },
      end: { line: selectionRange.end.y, character: selectionRange.end.x },
    };

    webviewBridge.$addToComposerContext({
      type: "selection",
      uri, // 可自定义标识来源
      content: selection,
      range,
      relativePath,
    });
  }

  async $locateByPath(maybeRelativePath: string): Promise<void> {
    const openTextDocumentWithNotification = (uri: Uri): Thenable<vscode.TextDocument> => {
      return vscode.workspace.openTextDocument(uri).then(e => e, (e) => {
        vscode.window.showErrorMessage(`无法定位该文件${uri.toString()} , 请确认文件是否存在`);
        this.logger.error(`locateByPathFailed:${uri.toString()}`, this.loggerScope, {
          err: e,
        });
        throw e;
      });
    };

    const workspacePath = vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath;
    const absolutePath = maybeRelativePath.startsWith("/") ? maybeRelativePath : workspacePath ? path.join(workspacePath, maybeRelativePath) : maybeRelativePath;
    const doc = await openTextDocumentWithNotification(vscode.Uri.file(absolutePath));
    await vscode.window.showTextDocument(doc, this.viewColumn);
  }

  async $postMessageToComposerEngine(data: InternalWebviewMessage): Promise<void> {
    const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);

    if (data.type === "newTask") {
      this.sessionState.sessionId = data.reqData.sessionId;
      this.sessionState.chatId = data.reqData.chatId;
      this.sessionState.currentConversationDelayedUnsavedState = {
        editorState: data.editorState,
        questionForHumanReading: data.questionForHumanReading,
        contextItems: data.contextItems,
      };
      /* 用于对比这次对话新增的错误 */
      this.sessionState.diagnosticsWhenNewTaskCreated = vscode.languages.getDiagnostics();
      const deviceInfo = buildDeviceLog();
      const project = curProjectInfos?.[0];

      // 获取IndexFileService中的索引状态
      const indexFileService = this.getService(IndexFileService);
      const isIndexed = indexFileService.indexState.indexed;

      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      const localMessages: LocalMessage[] = session?.localMessages || [];
      const apiConversationHistory = await apiConversationHistoryStorageService.getApiConversationHistory(this.sessionState.sessionId);
      const allowImage = this.getBase(ConfigManager).get(Config.AGENT_PREFERENCE) === "intelligent";
      const payload: WebviewMessage<"newTask"> = {
        ...data,
        // 过滤掉rule，不需要做为上下文
        contextItems: data.contextItems.filter(s => s.type !== "rule"),
        taskForLlm: await editorStateToContextualTextForLlm(data.editorState, cwd, data.contextItems),
        images: allowImage ? data.contextItems.filter(v => v.type === "remoteImage").map(i => i.uploadInfo.url || "") : undefined,
        localMessages,
        reqData: {
          ...data.reqData,
          messages: apiConversationHistory,
          username: this.getBase(GlobalStateManager).get(GlobalState.USER_INFO)?.name,
          deviceInfo: {
            deviceId: this.getBase(GlobalStateManager).get(GlobalState.DEVICE_ID),
            deviceModel: deviceInfo.deviceModel,
            deviceName: deviceInfo.deviceName,
            deviceOsName: os.type(),
            deviceOsVersion: deviceInfo.deviceOsVersion,
            ide: KwaipilotEnv.isInIde ? "kwaipilot-ide" : "vscode",
            ideVersion: KwaipilotEnv.isInIde ? vscode.appVersion : vscode.version,
            platform: KwaipilotEnv.isInIde ? "kwaipilot-ide" : "kwaipilot-vscode",
            pluginVersion: pkg.version,
          },
          projectInfo: {
            gitUrl: project?.gitRemote,
            openedFilePath: vscode.window.activeTextEditor?.document.fileName || "",
            projectName: project?.name,
          },
          model: this.userPreferredModel,
        },
      };
      /**
       * newTask 可能来源自历史消息的重新编辑\普通底部输入框的发送, 这两种情况都要重置 checkpoint 状态
       */
      await this.$setEditingMessageTs(undefined);
      await this.setCurrentMessageTs(undefined);

      // 当消息被处理后，将indexed状态关联到人类消息上
      // 使用一次性的检查来处理消息索引状态
      const currentTaskId = Date.now(); // 用于标识当前任务

      // 已有的messageList监听器会处理所有的消息列表事件
      // 我们在setupLocalServiceMessageListener中添加一个钩子来处理这次特定的任务消息
      // 保存当前任务信息供后续处理
      this.sessionState.pendingIndexedStatus = {
        taskId: currentTaskId,
        indexed: isIndexed,
      };

      this.logger.info("composer:newTask", this.loggerScope, {
        value: payload,
      });
      this.getBase(WebloggerManager).$reportUserAction({
        key: "composerNewTask",
        type: "composerNewTask",
        sessionId: this.sessionState.sessionId,
        chatId: data.reqData.chatId,
      });
      kwaipilotBinaryModule.request("assistant/agent/local", payload);
      /**
       * https://team.corp.kuaishou.com/task/B2478959
       * 如果用户从历史对话发起,应当检查当前的 indeterminatedWorkingSetEffects
       */
      this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects
        // 必须要在历史消息存在
        .filter(effect => payload.localMessages.some(m => effect.messageTs === m.ts));
    }
    else if (data.type === "stop") {
      // 作悲观处理
      this.sessionState.currentTaskInterrupted = true;
      // 取消正在应用中的文件
      this.abortComposerChat();
      await this.postComposerStateUpdate();
      kwaipilotBinaryModule.request("assistant/agent/local", data);
    }
    else if (data.type === "restore") {
      // 继续且回退，需要等待
      // https://team.corp.kuaishou.com/task/B2489487
      // 直接转发
      await kwaipilotBinaryModule.request("assistant/agent/local", {
        ...data,
        params: {
          ...data.params,
        },
      });
    }
    else {
      // 直接转发
      kwaipilotBinaryModule.request("assistant/agent/local", data);
    }
  }

  async setCurrentMessageTs(commitHash: string | undefined) {
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
    if (!session) {
      return;
    }
    const modified = produce(session, (draft) => {
      if (commitHash) {
        const target = draft?.localMessages.find(v => v.lastCheckpointHash === commitHash);
        if (target) {
          draft.currentMessageTs = target.ts;
        }
        else {
          throw new Error("message not found");
        }
      }
      else {
        draft.currentMessageTs = undefined;
      }
    });
    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
    this.postComposerStateUpdate(modified);
  }

  async $setEditingMessageTs(ts: number | undefined): Promise<void> {
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);

    if (!session) {
      return;
    }
    if (session.editingMessageTs === ts) {
      return;
    }
    const modified = produce(session, (draft) => {
      if (ts) {
        if (draft?.localMessages.find(v => v.ts === ts)) {
          draft.editingMessageTs = ts;
        }
        else {
          throw new Error("message not found");
        }
      }
      else {
        draft.editingMessageTs = undefined;
      }
    });

    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);

    this.postComposerStateUpdate(modified);
  }

  private async truncateApiConversationHistory(erasedChatId: string) {
    const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
    const apiConversationHistory = await apiConversationHistoryStorageService.getApiConversationHistory(this.sessionState.sessionId);
    const erasedChatIndex = apiConversationHistory.findIndex(v => v.chatId === erasedChatId);
    if (erasedChatIndex === -1) {
      this.logger.warn(`truncateApiConversationHistory:conversationIndexNotFound:${erasedChatId}`, this.loggerScope);
      return apiConversationHistory;
    }
    const truncated = apiConversationHistory.slice(0, erasedChatIndex);
    await apiConversationHistoryStorageService.setApiConversationHistory(
      this.sessionState.sessionId,
      truncated,
    );
    return truncated;
  }

  async $revertHistory({ humanMessageTs, updateStateImmediately }: { humanMessageTs: number; updateStateImmediately: boolean }): Promise<void> {
    // 更新历史记录
    const session = await this.getService(ComposerSessionStorageService).getComposerSessionData(this.sessionState.sessionId);
    if (!session) {
      throw new Error("session not found");
    }
    const humanMessageI = session.localMessages.findIndex(v => v.ts === humanMessageTs);
    if (humanMessageI === -1) {
      throw new Error("humanMessageTs not found");
    }
    const humanMessage = session.localMessages[humanMessageI];

    const modified = produce(session, (draft) => {
      draft.localMessages = draft.localMessages.slice(0, humanMessageI);
    });
    if (modified.localMessages.length === 0) {
      // 删除这条记录
      await this.getService(ComposerSessionStorageService).deleteSession(modified.sessionId);
      // 历史列表中也要删除
      await this.getService(ComposerHistoryStorageService).deleteHistoryItem(modified.sessionId);
      // 清理 BlockCode 缓存
      await this.getService(BlockCodeCacheStorageService).clearSessionCache(modified.sessionId);
    }
    else {
      await this.getService(ComposerSessionStorageService).setComposerSessionData(this.sessionState.sessionId, modified);
    }
    this.sessionState.currentTaskInterrupted = true;
    this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects
      // 必须要在历史消息存在
      .filter(effect => modified.localMessages.some(m => effect.messageTs === m.ts));
    if (updateStateImmediately) {
      this.postComposerStateUpdate(modified);
    }
    if (!humanMessage.chatId) {
      throw new Error("humanMessage.chatId not found");
    }

    await this.truncateApiConversationHistory(humanMessage.chatId);
  }

  /**
   * * restore 仓库状态
   * * 更新历史消息
   * * 更新 apiConversationList
   * @param param0
   */
  async $restoreCheckpoint({
    humanMessageTs,
    restoreCommitHash,
    updateStateImmediately,
  }: {
    humanMessageTs: number;
    restoreCommitHash: string;
    updateStateImmediately: boolean;
  }) {
    await this.$postMessageToComposerEngine({
      type: "restore",
      params: {
        sessionId: this.sessionState.sessionId,
        restoreCommitHash,
      },
    });

    await this.$revertHistory({ humanMessageTs, updateStateImmediately });
  }

  async $getDiffSet(lhsHash: string, rhsHash?: string): Promise<DiffSet[]> {
    const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    const response = await kwaipilotBinaryModule.request("assistant/agent/getDiffSet", { sessionId: this.sessionState.sessionId, lhsHash, rhsHash });
    return response.data || [];
  }

  async $setCurrentModel(model: SupportedModels): Promise<void> {
    const globalStateManager = this.getBase(GlobalStateManager);
    await globalStateManager.update(GlobalState.COMPOSER_PREFERRED_MODEL, model);
    this.userPreferredModel = model;
    await this.postComposerStateUpdate();
  }

  /**
   * 切换专注模式
   * @param enable 是否开启专注模式
   */
  async $toggleFocusMode(enable: boolean): Promise<void> {
    this.sessionState.focusMode = enable;

    // 获取当前会话数据并更新focusMode字段
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const currentSession = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);

    if (currentSession) {
      // 更新持久化数据中的focusMode字段
      const updatedSession: PersistedComposerSessionData = {
        ...currentSession,
        focusMode: enable,
      };
      await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, updatedSession);
      await this.postComposerStateUpdate(updatedSession);
    }
    else {
      // 如果没有现有会话数据，直接调用postComposerStateUpdate
      await this.postComposerStateUpdate();
    }
  }

  $editorStateToContextualTextForLlm(editorState: SerializedEditorState, cwd: string, contextItems: MentionNodeV2Structure[]): Promise<string> {
    return editorStateToContextualTextForLlm(editorState, cwd, contextItems);
  }

  /**
   * editor-ext
   * 编辑区操作文件
   */
  setupEditorFileActionListener() {
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.acceptDiff", (param: vscode.Uri) => {
        const relativePath = vscode.workspace.asRelativePath(param.fsPath);
        this.editFile.acceptFile({ filepaths: [relativePath] });
        this.updateFileStatus({
          filepaths: [relativePath],
          type: "keep",
        });
      }),
    );
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.rejectDiff", (param: vscode.Uri) => {
        const relativePath = vscode.workspace.asRelativePath(param.fsPath);
        this.editFile.rejectFile({ filepaths: [relativePath] });
        this.updateFileStatus({
          filepaths: [relativePath],
          type: "undo",
        });
      }),
    );
  }

  /**
   * webview-ui -> ext -> kwaipilot-binary
   */
  setupWebviewMessageListener() {
    const bridge = this.getBase(Bridge);
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK, async (payload) => {
      if (!payload) {
        this.logger.warn("收到无效的 DIFF_FEEDBACK 事件", this.loggerScope);
        return;
      }
      const filepaths = payload.filepath
        ? [payload.filepath]
        : uniq(this.sessionState.indeterminatedWorkingSetEffects.filter(v => v.state === "applied").map(v => v.path));
      if (payload.type === "keep") {
        if (!payload.filepath) {
          await this.editFile.acceptAll();
        }
        else {
          await this.editFile.acceptFile({ filepaths: [payload.filepath] });
        }
      }
      else if (payload.type === "undo") {
        const partialRejectPaths = payload.partialPaths;
        if (!payload.filepath) {
          await this.editFile.rejectAll(partialRejectPaths);
        }
        else {
          await this.editFile.rejectFile({ filepaths: [payload.filepath], partialPaths: partialRejectPaths });
        }
      }

      await this.updateFileStatus({
        filepaths,
        type: payload.type,
        filesStatus: payload.filesStatus,
      });
      // 如果是离开会话的全部拒绝，需要将 appling中的文件abort并 reject掉。

      if (payload.abortChat) {
        await this.abortComposerChat();
      }
    });
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_APPLY_FILE, async (payload) => {
      if (!payload) {
        this.logger.warn("收到无效的 APPLY_FILE 事件", this.loggerScope);
        return;
      }
      const { message } = payload;
      if (!message) {
        this.logger.warn("收到无效的 APPLY_FILE 事件", this.loggerScope);
        return;
      }
      const toolInfo = JSON.parse(message.text || "{}") as SayTool;
      if (!toolInfo.path || !toolInfo.content) {
        throw new Error("invalid toolInfo");
      }
      const absoluteFilePath = path.resolve(cwd, toolInfo.path);
      let before = "";
      try {
        before = await extractTextFromFile(absoluteFilePath);
      }
      catch (e) {
        // ignore
      }
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }
      const relPath = vscode.workspace.asRelativePath(toolInfo.path);
      if (this.editFile.getFileDiffState(relPath)) {
        await this.updateFileStatus({
          filepaths: [relPath],
          type: "keep",
        });
      }

      this.setIndeterminateFileStatus(message, "applying");
      this.postComposerStateUpdate(session);

      try {
        if (toolInfo.tool_version === "v2") {
          // 新版不再用 instant/apply 的方式
          await this.editFile.writeToFileV1(
            {
              path: toolInfo.path,
              before,
              after: toolInfo.content,
            },
          );
        }
        else {
          await this.editFile.writeToFile(
            {
              path: toolInfo.path,
              content: toolInfo.content,
              language: (toolInfo.language as any) || "default",
              instructions: toolInfo.instructions,
            },
            {
              sessionId: this.sessionState.sessionId,
              chatId: message.chatId || "",
              source: "agent-v2",
            },
          );
        }

        let diffContent: DiffContent | undefined = undefined;
        const after = await extractTextFromFile(absoluteFilePath);
        // 检查是否已经传入了新旧内容，如果是，则调用parseDiffContent
        diffContent = this.compareContents(before, after);
        const noModifyed = diffContent?.addedLines.length === 0 && diffContent?.deletedLines.length === 0;
        if (noModifyed) {
          this.editFile.acceptFile({ filepaths: [relPath] });
          this.removeIndeterminateFileStatus({ messageTs: message.ts });
        }
        else {
          this.setIndeterminateFileStatus(message, "applied");
        }

        let modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === message.ts, "accepted");
        modified = await this.updateEditFileDiffContent(modified, m => m.ts === message.ts, diffContent);
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        // apply 不算做解除中断 this.currentTaskInterrupted = false;
        this.postComposerStateUpdate(modified);
      }
      catch (e) {
        this.removeIndeterminateFileStatus({ messageTs: message.ts });
        const modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === message.ts, "init");
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        // apply 不算做解除中断 this.currentTaskInterrupted = false;
        this.postComposerStateUpdate(modified);
        throw e;
      }
    });
    // 备用，暂时没有用到，未经过验证的方法
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_SAVE, async (payload) => {
      const { ts, filepath, diffContent } = payload || {};
      if (!filepath || !diffContent) {
        this.logger.warn("收到无效的 DIFF_SAVE 事件", this.loggerScope);
        return;
      }
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }

      let modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === ts, "accepted");
      modified = await this.updateEditFileDiffContent(modified, m => m.ts === ts, diffContent);
      await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
      // apply 不算做解除中断 this.currentTaskInterrupted = false;
      this.postComposerStateUpdate(modified);
    });
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_TOGGLE_TERMINAL, async () => {
      this.terminalManager.toggleTerminal();
    });
  }

  /**
   * 注册接收本地包中 助理模式 引擎的消息
   */
  setupLocalServiceMessageListener() {
    const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const composerHistoryStorageService = this.getService(ComposerHistoryStorageService);
    const bridge = this.getBase(Bridge);
    kwaipilotBinaryModule.onMessage(
      "assistant/agent/writeToFile",
      async ({ data: { path: dataPath, content } }) => {
        const absoluteFilePath = path.resolve(cwd, dataPath);
        let before = "";
        try {
          before = await extractTextFromFile(absoluteFilePath);
        }
        catch (e) {
          // ignore
        }
        // 更新 workingSet
        // 更新这条消息的状态
        this.logger.info("工具调用searchAndReplace", this.loggerScope, {
          value: {
            path: dataPath,
            content,
          },
        });
        const session = await composerSessionStorageService.getComposerSessionData(
          this.sessionState.sessionId,
        );
        if (!session) {
          throw new Error("session not found");
        }
        const targetMessage = findLast(session?.localMessages, msg =>
          isToolEditFileMessage(msg),
        );
        if (!targetMessage) {
          throw new Error("targetMessage not found");
        }
        const relPath = vscode.workspace.asRelativePath(dataPath);
        if (this.editFile.getFileDiffState(relPath)) {
          await this.updateFileStatus({
            filepaths: [relPath],
            type: "keep",
          });
        }
        this.setIndeterminateFileStatus(targetMessage, "applying");

        this.sessionState.currentTaskInterrupted = false;
        this.sessionState.localServiceConnectionLost = false;
        this.postComposerStateUpdate(session);
        try {
          const details = await this.editFile.writeToFileV1(
            {
              path: relPath,
              before,
              after: content,
            },
          );

          // 生成差异内容
          const after = content;
          const diffContent = this.compareContents(before, after);
          const noModified
            = diffContent?.addedLines.length === 0
            && diffContent?.deletedLines.length === 0;

          if (noModified) {
            this.editFile.acceptFile({ filepaths: [relPath] });
            this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
          }
          else {
            this.setIndeterminateFileStatus(targetMessage, "applied");
          }

          // 获取最新会话数据并更新状态
          const latestSession
            = await composerSessionStorageService.getComposerSessionData(
              this.sessionState.sessionId,
            );
          if (!latestSession) {
            throw new Error("session not found");
          }
          let modified = await this.updateEditFileMessageFileStatus(
            latestSession,
            m => m.ts === targetMessage.ts,
            "accepted",
          );
          modified = await this.updateEditFileDiffContent(
            modified,
            m => m.ts === targetMessage.ts,
            diffContent,
          );
          await composerSessionStorageService.setComposerSessionData(
            this.sessionState.sessionId,
            modified,
          );
          this.postComposerStateUpdate(modified);

          return {
            data: { ...details, noModified },
            status: "ok",
          };
        }
        catch (e) {
          this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
          const modified = this.updateEditFileMessageFileStatus(
            session,
            m => m.ts === targetMessage.ts,
            "init",
          );
          await composerSessionStorageService.setComposerSessionData(
            this.sessionState.sessionId,
            modified,
          );

          this.postComposerStateUpdate(modified);
          throw e;
        }
      },
    );
    kwaipilotBinaryModule.onMessage("assistant/agent/message", async ({ data: partialMessage }) => {
      this.logger.info(`receivePartialMessage`, this.loggerScope, {
        value: partialMessage,
      });
      if (partialMessage.sessionId !== this.sessionState.sessionId) {
        this.logger.warn(`sessionId不匹配 ${partialMessage.sessionId} !== ${this.sessionState.sessionId}`, this.loggerScope, {
          value: partialMessage,
        });
        return;
      }
      const savedMessage = (await composerSessionStorageService.getComposerSessionData(partialMessage.sessionId))?.localMessages.find(v => v.ts === partialMessage.ts);
      if (savedMessage && !savedMessage.partial) {
        // ide-agent 的 message 消息是 throttled, 可能会在消息完成后 messageList 之后发出 partial false 的消息
        // 因此 agent/message 的 partial=false 的消息, 应当由 messageList 处理
        return;
      }
      const toInternalLocalMessage = (localMessage: LocalMessage): InternalLocalMessage => {
        if (isHumanMessage(localMessage)) {
          if (!this.sessionState.currentConversationDelayedUnsavedState) {
            throw new Error("currentConversationDelayedUnsavedState is not set");
          }

          return {
            ...localMessage,
            role: "user" as const,
            editorState: this.sessionState.currentConversationDelayedUnsavedState.editorState,
            contextItems: this.sessionState.currentConversationDelayedUnsavedState.contextItems,
            diagnostics: [],
          };
        }
        return {
          ...localMessage,
          role: undefined,
        };
      };
      // partial message 不需要存储 sqlite
      const internalPartialMessage = toInternalLocalMessage(partialMessage);
      bridge.postOneWayMessage(
        this.getBase(Webview)._view!.webview,
        WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE, {
          partialMessage: internalPartialMessage,
        });
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/messageList", async ({ data: messages }) => {
      this.logger.info(`receiveMessageList`, this.loggerScope, {
        value: messages,
      });
      await this.lock.acquire(LOCK_KEY_GET_LOCAL_MESSAGES, async () => {
        const { sessionId } = messages[0];
        if (sessionId !== this.sessionState.sessionId) {
          return;
        }

        const s = Date.now();
        const originalData: PersistedComposerSessionData = await composerSessionStorageService.getComposerSessionData(sessionId) || {
          workspaceUri: this.context.storageUri?.toString() || "",
          localMessages: [],
          sessionId,
          editingMessageTs: undefined,
          currentMessageTs: undefined,
        };

        if (!originalData) {
          throw new Error("session data for sessionId not found");
        }

        const localMessageMapper = await createLocalMessageMapper({
          context: {
            state: this.sessionState,
            composerSessionStorageService,
            webloggerManager: this.getBase(WebloggerManager),
            configManager: this.getBase(ConfigManager),
            cwd,
          },
        });

        const modifiedData = produce(originalData, (draft) => {
          draft.localMessages = messages.map(localMessageMapper);
          return draft;
        });

        // 从 engine 获取到通信时间，说明肯定不是中断的
        this.sessionState.currentTaskInterrupted = false;
        // 从 engine 获取到通信时间，说明连接已恢复
        this.sessionState.localServiceConnectionLost = false;
        // 消息有变化，需要通知 webview-ui
        this.postComposerStateUpdate(modifiedData);
        await Promise.all([
          composerHistoryStorageService.getComposerHistoryItem(sessionId)
            .then(historyItem =>
              composerHistoryStorageService.updateById(sessionId, {
                sessionId,
                name: historyItem?.name || truncateToSessionName(this.sessionState.currentConversationDelayedUnsavedState?.questionForHumanReading || "新对话"),
                workspaceUri: modifiedData.workspaceUri,
              }),
            ),
          composerSessionStorageService.setComposerSessionData(sessionId, modifiedData),
        ]);
        this.logger.info(`messageLoading:messageList:done:cost ${Date.now() - s}`, this.loggerScope);
      });
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/apiConversationList", async ({ data: apiConversationHistory }) => {
      const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
      await apiConversationHistoryStorageService.setApiConversationHistory(this.sessionState.sessionId, apiConversationHistory);
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/executeCommand", async ({ data: { command, is_background } }) => {
      type Response = Awaited<ToIdeFromCoreProtocol["assistant/agent/executeCommand"][1]>;
      if (is_background) {
        this.executeCommandTool(command);
        const res: Response = {
          data: {
            userRejected: false,
            result: "",
            completed: false,
          },
          status: "ok",
        };
        return res;
      }
      try {
        const { userRejected, result, completed } = await new Promise<ExecuteCommandResponse>((resolve, reject) => {
          this.executeCommandTool(command).then((res) => {
            resolve(res);
          });
          setTimeout(() => {
            reject(new Error("超时, 命令运行过长, 或在等待用户输入"));
          }, 30 * 1000);
        });
        const res: Response = {
          data: {
            userRejected,
            result,
            completed,
          },
          status: "ok",
        };
        return res;
      }
      catch (e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        const res: Response = {
          data: {
            userRejected: false,
            result: `命令运行失败: ${errorMessage}`,
            completed: false,
          },
          status: "ok",
        };
        return res;
      }
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/environment", async ({ data: { includeFileDetails } }) => {
      const details = await this.getEnvironmentDetails(includeFileDetails);
      return {
        data: details,
        status: "ok",
      };
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/editFile", async ({ data: { path: dataPath, content, language, instructions } }) => {
      const absoluteFilePath = path.resolve(cwd, dataPath);
      let before = "";
      try {
        before = await extractTextFromFile(absoluteFilePath);
      }
      catch (e) {
        // ignore
      }
      // 更新 workingSet
      // 更新这条消息的状态
      this.logger.info("工具调用editFile", this.loggerScope, {
        value: {
          path: dataPath,
          content,
          language,
          instructions,
        },
      });
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }
      const targetMessage = findLast(session?.localMessages, msg => isPersistedToolEditFileMessage(msg));
      if (!targetMessage) {
        throw new Error("targetMessage not found");
      }
      const relPath = vscode.workspace.asRelativePath(dataPath);
      if (this.editFile.getFileDiffState(relPath)) {
        await this.updateFileStatus({
          filepaths: [relPath],
          type: "keep",
        });
      }
      this.setIndeterminateFileStatus(targetMessage, "applying");

      this.sessionState.currentTaskInterrupted = false;
      this.sessionState.localServiceConnectionLost = false;
      this.postComposerStateUpdate(session);
      try {
        const details = await this.editFile.writeToFile({
          path: dataPath,
          content,
          instructions,
          language,
          composer: true,
        }, {
          sessionId: this.sessionState.sessionId,
          chatId: targetMessage.chatId || "",
          source: "agent-v2",
        });
        let diffContent: DiffContent | undefined = undefined;
        if (details?.type === "success") {
          const after = await extractTextFromFile(absoluteFilePath);
          diffContent = this.compareContents(before, after);
          const noModifyed = diffContent?.addedLines.length === 0 && diffContent?.deletedLines.length === 0;
          if (noModifyed) {
            this.editFile.acceptFile({ filepaths: [relPath] });
            this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
          }
          else {
            this.setIndeterminateFileStatus(targetMessage, "applied");
          }
        }

        /* .writeToFile 可能会很长，在此期间 session 可能已经更新了，所以需要获取最新的 session */
        const latestSession = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
        if (!latestSession) {
          throw new Error("session not found");
        }
        let modified = await this.updateEditFileMessageFileStatus(latestSession, m => m.ts === targetMessage.ts, details?.type === "success" ? "accepted" : "rejected");
        modified = await this.updateEditFileDiffContent(modified, m => m.ts === targetMessage.ts, diffContent);
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        this.postComposerStateUpdate(modified);
        return {
          data: details,
          status: "ok",
        };
      }
      catch (e) {
        this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
        const modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === targetMessage.ts, "init");
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);

        this.postComposerStateUpdate(modified);
        // FIXME: 和本地包通信，应该由通信层解决错误处理
        throw e;
      }
    });
    kwaipilotBinaryModule.on("restart", async () => {
      // 如果当前正在流式输出中, 会导致丢失状态
      if (!this.sessionState.sessionId) {
        return;
      }
      this.sessionState.localServiceConnectionLost = true;
      this.sessionState.currentTaskInterrupted = true;
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }
      this.postComposerStateUpdate(session);
    });
  }

  /**
   * 终止对话的副作用
   * @param message
   * @param status
   */
  async abortComposerChat() {
    const applyingFiles = uniq(this.sessionState.indeterminatedWorkingSetEffects.filter(v => v.state === "applying").map(v => v.path));
    for (const relPath of applyingFiles) {
      await this.editFile.abortApply(relPath);
    }
    await this.updateFileStatus({
      filepaths: applyingFiles,
      type: "undo",
    });
  }

  private async setIndeterminateFileStatus(message: InternalLocalMessage, status: FileIndeterminateStateType) {
    const toolInfo = JSON.parse(message.text || "{}") as SayTool;
    const samePathEffectI = this.sessionState.indeterminatedWorkingSetEffects.findIndex(v => v.path === toolInfo.path);
    if (samePathEffectI !== -1 && this.sessionState.indeterminatedWorkingSetEffects[samePathEffectI].state === "applied") {
      //  之前的消息没有接受 or 拒绝，则清空，默认接受
      this.sessionState.indeterminatedWorkingSetEffects.splice(samePathEffectI, 1);
    }
    const messageTs = message.ts;
    const targetI = this.sessionState.indeterminatedWorkingSetEffects.findIndex(i => i.messageTs === messageTs);
    if (targetI === -1) {
      this.sessionState.indeterminatedWorkingSetEffects.push({ messageTs, state: status, path: toolInfo.path || "" });
    }
    else {
      this.sessionState.indeterminatedWorkingSetEffects[targetI].state = status;
    }
  }

  private async removeIndeterminateFileStatus(selector: { messageTs?: number; path?: string }) {
    const predicate: (v: IndeterminatedWorkingSetEffectState) => boolean = selector.messageTs
      ? v => v.messageTs === selector.messageTs
      : selector.path
        ? v => v.path === selector.path
        : () => false;
    const targetI = this.sessionState.indeterminatedWorkingSetEffects.findIndex(predicate);
    if (targetI === -1) {
      return;
    }
    this.sessionState.indeterminatedWorkingSetEffects.splice(targetI, 1);
  }

  /**
   * 按文件路径分别更新消息文件状态
   * @param session 会话数据
   * @param filesStatus 文件状态映射，键为文件路径，值为状态
   * @returns 修改后的会话数据
   */
  private updateEditFileMessageFileStatusByPath(
    session: PersistedComposerSessionData,
    filesStatus: { [filepath: string]: "accepted" | "rejected" },
  ) {
    /**
     * 更新 sqlite
     */
    const modified: PersistedComposerSessionData = produce(session, (draft) => {
      const targets = draft.localMessages
        .filter(isToolEditFileMessage) as InternalLocalMessage_Tool_EditFile[];

      targets.forEach((target) => {
        const toolInfo = JSON.parse(target.text || "{}") as SayTool;
        const filePath = toolInfo.path || "";

        // 检查该文件是否在 filesStatus 中有对应的状态
        if (filesStatus[filePath]) {
          target.workingSetEffect = target.workingSetEffect || {
            status: "init",
            language: toolInfo.language || "",
            path: filePath,
            diffContent: undefined,
          };
          target.workingSetEffect.status = filesStatus[filePath];
        }
      });
    });

    return modified;
  }

  /**
   * 更新编辑文件消息的文件状态
   * @param session 会话数据
   * @param selector 消息选择器函数
   * @param persistedStatus 持久化状态
   * @returns 修改后的会话数据
   */
  private updateEditFileMessageFileStatus(
    session: PersistedComposerSessionData,
    selector: (message: InternalLocalMessage_Tool_EditFile) => boolean,
    persistedStatus: FilePersistedStateType,
  ) {
    /**
     * 更新 sqlite
     */
    const modified: PersistedComposerSessionData = produce(session, (draft) => {
      const targets = draft.localMessages
        .filter(isToolEditFileMessage)
        .filter(m => selector(m as InternalLocalMessage_Tool_EditFile)) as InternalLocalMessage_Tool_EditFile[];
      targets.forEach((target) => {
        const toolInfo = JSON.parse(target.text || "{}") as SayTool;
        target.workingSetEffect = target.workingSetEffect || {
          status: "init",
          language: toolInfo.language || "",
          path: toolInfo.path || "",
          diffContent: undefined,
        };
        target.workingSetEffect.status = persistedStatus;
      });
    });

    return modified;
  }

  private async updateEditFileDiffContent(
    session: PersistedComposerSessionData,
    selector: (message: InternalLocalMessage_Tool_EditFile) => boolean,
    diffContent?: DiffContent | null,
  ) {
    /**
     * 更新 sqlite
     */
    const modified: PersistedComposerSessionData = produce(session, (draft) => {
      const targets = draft.localMessages
        .filter(isToolEditFileMessage)
        .filter(m => selector(m as InternalLocalMessage_Tool_EditFile)) as InternalLocalMessage_Tool_EditFile[];
      targets.forEach((target) => {
        const toolInfo = JSON.parse(target.text || "{}") as SayTool;
        target.workingSetEffect = target.workingSetEffect || {
          status: "init",
          language: toolInfo.language || "",
          path: toolInfo.path || "",
          diffContent: undefined,
        };
        target.workingSetEffect.diffContent = diffContent || undefined;
      });
    });

    return modified;
  }

  async updateFileStatus(payload: {
    filepaths: string[];
    type: "keep" | "undo";
    filesStatus?: { [filepath: string]: "accepted" | "rejected" };
  }) {
    // 更新状态
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
    if (!session) {
      throw new Error("session not found");
    }
    this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects.filter(v => !payload.filepaths.includes(v.path));

    let modified: PersistedComposerSessionData;

    // 如果提供了 filesStatus，则使用按文件路径分别设置状态的方式
    if (payload.filesStatus && Object.keys(payload.filesStatus).length > 0) {
      modified = this.updateEditFileMessageFileStatusByPath(session, payload.filesStatus);
    }
    else {
      // 兼容旧的方式：使用统一的 type 来设置所有文件的状态
      modified = this.updateEditFileMessageFileStatus(session, /* 更新所有消息 */(m) => {
        const toolInfo = JSON.parse(m.text || "{}") as SayTool;
        return payload.filepaths.includes(toolInfo.path || "");
      }, payload.type === "keep" ? "accepted" : "rejected");
    }

    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
    this.postComposerStateUpdate(modified);
  }

  protected postComposerStateUpdate(): Promise<void>;
  protected postComposerStateUpdate(modified: PersistedComposerSessionData): void;
  protected async postComposerStateUpdate(modified?: PersistedComposerSessionData) {
    if (!modified) {
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session: PersistedComposerSessionData | undefined = this.sessionState.sessionId
        ? await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId)
        : {
            localMessages: [],
            workspaceUri: "",
            sessionId: "",
            editingMessageTs: undefined,
            currentMessageTs: undefined,
            focusMode: false,
          };

      if (!session) {
        // 新建聊天时 session 可能还未初始化，返回一个空的 session 数据结构
        modified = {
          localMessages: [],
          workspaceUri: "",
          sessionId: this.sessionState.sessionId || "",
          editingMessageTs: undefined,
          currentMessageTs: undefined,
          userPreferredModel: this.userPreferredModel,
          focusMode: this.sessionState.focusMode || false,
        } as unknown as PersistedComposerSessionData;
      }
      else {
        // 确保从数据库获取的session数据包含最新的focusMode状态
        // 如果sessionState中有focusMode值，使用它；否则使用session中的值，如果session中也没有则默认为false
        modified = {
          ...session,
          focusMode: this.sessionState.focusMode !== undefined ? this.sessionState.focusMode : (session.focusMode ?? false),
        };
      }
    }
    const bridge = this.getBase(Bridge);
    bridge.postOneWayMessage(
      this.getBase(Webview)._view!.webview,
      WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE, {
        localMessages: modified.localMessages,
        workspaceUri: modified.workspaceUri,
        sessionId: modified.sessionId,
        currentTaskInterrupted: this.sessionState.currentTaskInterrupted,
        indeterminatedWorkingSetEffects: this.sessionState.indeterminatedWorkingSetEffects,
        isCurrentWorkspaceSession: this.context.storageUri?.toString() === modified.workspaceUri,
        editingMessageTs: modified.editingMessageTs,
        currentMessageTs: modified.currentMessageTs,
        userPreferredModel: this.userPreferredModel,
        localServiceConnectionLost: this.sessionState.localServiceConnectionLost,
        indexed: modified.indexed,
        // 传递缓存路径信息给前端
        cachePathInfos: modified.cachePathInfos || [],
        // 传递专注模式状态给前端
        focusMode: this.sessionState.focusMode || false,
      });
  }

  async getEnvironmentDetails(includeFileDetails: boolean = false) {
    let details = "";
    const isInnerImage = (absolutePath: string) => {
      const ext = path.extname(absolutePath).toLowerCase();
      const isImage = [".png", ".jpeg", ".jpg"].includes(ext);
      if (isImage) {
        return absolutePath.startsWith(cwd);
      }
      return true;
    };
    // It could be useful for cline to know if the user went from one or no file to another between messages, so we always include this context
    details += "\n\n# VSCode Visible Files";
    const visibleFilePaths = vscode.window.visibleTextEditors
      ?.map(editor => editor.document?.uri?.fsPath)
      .filter(Boolean)
      .filter(Boolean).filter(absolutePath => isInnerImage(absolutePath))
      .map(absolutePath => path.relative(cwd, absolutePath));

    // Filter paths through clineIgnoreController
    const allowedVisibleFiles = visibleFilePaths
      .map(p => toPosixPath(p))
      .join("\n");

    if (allowedVisibleFiles) {
      details += `\n${allowedVisibleFiles}`;
    }
    else {
      details += "\n(No visible files)";
    }

    details += "\n\n# VSCode Open Tabs";
    const openTabPaths = vscode.window.tabGroups.all
      .flatMap(group => group.tabs)
      .map(tab => (tab.input as vscode.TabInputText)?.uri?.fsPath)
      .filter(Boolean).filter(absolutePath => isInnerImage(absolutePath))
      .map(absolutePath => path.relative(cwd, absolutePath));

    // Filter paths through clineIgnoreController
    const allowedOpenTabs = openTabPaths
      .map(p => toPosixPath(p))
      .join("\n");

    if (allowedOpenTabs) {
      details += `\n${allowedOpenTabs}`;
    }
    else {
      details += "\n(No open tabs)";
    }

    const busyTerminals = this.terminalManager.getTerminals(true);
    const inactiveTerminals = this.terminalManager.getTerminals(false);
    // const allTerminals = [...busyTerminals, ...inactiveTerminals]

    if (busyTerminals.length > 0) {
      //  || this.didEditFile
      await delay(300); // delay after saving file to let terminals catch up
    }

    // let terminalWasBusy = false
    if (busyTerminals.length > 0) {
      // wait for terminals to cool down
      // terminalWasBusy = allTerminals.some((t) => this.terminalManager.isProcessHot(t.id))
      await pWaitFor(() => busyTerminals.every(t => !this.terminalManager.isProcessHot(t.id)), {
        interval: 100,
        timeout: 15_000,
      }).catch(() => { });
    }

    // waiting for updated diagnostics lets terminal output be the most up-to-date possible
    let terminalDetails = "";
    if (busyTerminals.length > 0) {
      // terminals are cool, let's retrieve their output
      terminalDetails += "\n\n# Actively Running Terminals";
      for (const busyTerminal of busyTerminals) {
        terminalDetails += `\n## Original command: \`${busyTerminal.lastCommand}\``;
        const newOutput = this.terminalManager.getUnretrievedOutput(busyTerminal.id);
        if (newOutput) {
          terminalDetails += `\n### New Output\n${newOutput}`;
        }
        else {
          // details += `\n(Still running, no new output)` // don't want to show this right after running the command
        }
      }
    }
    // only show inactive terminals if there's output to show
    if (inactiveTerminals.length > 0) {
      const inactiveTerminalOutputs = new Map<number, string>();
      for (const inactiveTerminal of inactiveTerminals) {
        const newOutput = this.terminalManager.getUnretrievedOutput(inactiveTerminal.id);
        if (newOutput) {
          inactiveTerminalOutputs.set(inactiveTerminal.id, newOutput);
        }
      }
      if (inactiveTerminalOutputs.size > 0) {
        terminalDetails += "\n\n# Inactive Terminals";
        for (const [terminalId, newOutput] of inactiveTerminalOutputs) {
          const inactiveTerminal = inactiveTerminals.find(t => t.id === terminalId);
          if (inactiveTerminal) {
            terminalDetails += `\n## ${inactiveTerminal.lastCommand}`;
            terminalDetails += `\n### New Output\n${newOutput}`;
          }
        }
      }
    }

    if (terminalDetails) {
      details += terminalDetails;
    }

    // Add current time information with timezone
    const now = new Date();
    const formatter = new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "numeric",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      hour12: true,
    });
    const timeZone = formatter.resolvedOptions().timeZone;
    const timeZoneOffset = -now.getTimezoneOffset() / 60; // Convert to hours and invert sign to match conventional notation
    const timeZoneOffsetStr = `${timeZoneOffset >= 0 ? "+" : ""}${timeZoneOffset}:00`;
    details += `\n\n# Current Time\n${formatter.format(now)} (${timeZone}, UTC${timeZoneOffsetStr})`;

    if (includeFileDetails) {
      details += `\n\n# Current Working Directory (${toPosixPath(cwd)}) Files\n`;
      const isDesktop = arePathsEqual(cwd, path.join(os.homedir(), "Desktop"));
      if (isDesktop) {
        // don't want to immediately access desktop since it would show permission popup
        details += "(Desktop files not shown automatically. Use list_files to explore if needed.)";
      }
      else {
        const [files, didHitLimit] = await listFiles(cwd, true, 200);
        const result = formatFilesList(cwd, files, didHitLimit);
        details += result;
      }
    }

    return `<environment_details>\n${details.trim()}\n</environment_details>`;
  }

  async executeCommandTool(command: string): Promise<ExecuteCommandResponse> {
    const terminalInfo = await this.terminalManager.getOrCreateTerminal(cwd);
    terminalInfo.terminal.show(); // weird visual bug when creating new terminals (even manually) where there's an empty space at the top.
    const process = this.terminalManager.runCommand(terminalInfo, command);

    let result = "";
    process.on("line", (line) => {
      result += line + "\n";
    });

    let completed = false;
    process.once("completed", () => {
      completed = true;
    });

    process.once("no_shell_integration", async () => {
      console.warn("no_shell_integration");
    });

    await process;

    // Wait for a short delay to ensure all messages are sent to the webview
    // This delay allows time for non-awaited promises to be created and
    // for their associated messages to be sent to the webview, maintaining
    // the correct order of messages (although the webview is smart about
    // grouping command_output messages despite any gaps anyways)
    await delay(50);

    result = result.trim();

    return {
      userRejected: false,
      result,
      completed,
    };
  }

  private decideCurrentTaskInterrupted(localMessages: InternalLocalMessage[]) {
    const lastMessage = localMessages.at(-1);
    if (
      (lastMessage?.type === "say" && lastMessage.say === "tool")
      || (lastMessage?.type === "ask" && lastMessage.ask === "tool")
      || (lastMessage?.type === "say" && lastMessage.say === "command")
      || (lastMessage?.type === "ask" && lastMessage.ask === "command")
      || (lastMessage?.type === "say" && lastMessage.say === "command_output")
      || (lastMessage?.type === "ask" && lastMessage.ask === "use_mcp_tool")
      || (lastMessage?.type === "say" && lastMessage.say === "use_mcp_tool_result")
      || (lastMessage?.type === "say" && lastMessage.say === "edit_file_result")
      || (lastMessage?.type === "say" && lastMessage.say === "checkpoint_created")
      || (lastMessage?.type === "say" && lastMessage.say === "api_req_started")
      // 普通的文本消息， 比如 "好的" "收到" "正在处理" 等，应当流转到 complete_result 等状态 因此也算 interrupted
      || (lastMessage?.type === "say" && lastMessage.say === "text")
      || (lastMessage?.partial === true)
    ) {
      return true;
    }
    return false;
  }

  async $showTaskWithId(sessionId: string) {
    // 切换 session 时， 切换历史记录
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    // 使用带缓存的方法获取会话数据
    const sessionData = await composerSessionStorageService.getComposerSessionDataWithCache(sessionId);
    this.resetTaskState();
    Object.assign(this.sessionState, {
      sessionId,
      currentTaskInterrupted: sessionData ? this.decideCurrentTaskInterrupted(sessionData.localMessages) : false,
      indeterminatedWorkingSetEffects: [],
      // 从持久化数据中恢复focusMode状态，如果不存在则默认为false（向后兼容）
      focusMode: sessionData?.focusMode ?? false,
    });
    if (sessionData) {
      await this.postComposerStateUpdate(sessionData);
    }
  }

  private resetTaskState() {
    this.sessionState = {
      sessionId: "",
      chatId: "",
      currentConversationDelayedUnsavedState: undefined,
      indeterminatedWorkingSetEffects: [],
      currentTaskInterrupted: false,
      localServiceConnectionLost: false,
      diagnosticsWhenNewTaskCreated: [],
    };
  }

  /**
 * 对比两个内容并返回差异块
 * @param originalContent 原始内容
 * @param modifiedContent 修改后的内容
 * @returns 差异块数组
 */
  private compareContents(originalContent: string, modifiedContent: string): DiffContent {
    // 使用diffLines比较内容
    const changes = diffLines(originalContent, modifiedContent);
    const withDiffAddLineNumber: number[] = [];
    const withDiffDeletedLineNumber: number[] = [];
    let code = "";

    let modifiedLineNumber = 1; // 修改后的内容的当前行号（从1开始）
    let maxCharacterNumber = 0; // 当前行最大字符数

    // 遍历所有变化
    for (let i = 0; i < changes.length; i++) {
      const change = changes[i];
      const value = change.value;
      code += value;
      // 如果是添加的部分或者修改的部分
      if (change.added) {
        // 将新增的内容分割为行
        const addedLines = change.value.split("\n");
        // 如果最后一行是空行（来自换行符），则移除
        if (addedLines[addedLines.length - 1] === "") {
          addedLines.pop();
        }
        for (const line of addedLines) {
          if (line.length > maxCharacterNumber) {
            maxCharacterNumber = line.length;
          }
          withDiffAddLineNumber.push(modifiedLineNumber);
          modifiedLineNumber++;
        }
      }
      else if (change.removed) {
        const deletedLines = change.value.split("\n");

        if (deletedLines[deletedLines.length - 1] === "") {
          deletedLines.pop();
        }
        for (const line of deletedLines) {
          if (line.length > maxCharacterNumber) {
            maxCharacterNumber = line.length;
          }
          withDiffDeletedLineNumber.push(modifiedLineNumber);
          modifiedLineNumber++;
        }
      }
      else {
        // 未修改的部分，只需更新行号
        const lines = change.value.split("\n");
        if (lines[lines.length - 1] === "") {
          lines.pop();
        }
        for (const line of lines) {
          if (line.length > maxCharacterNumber) {
            maxCharacterNumber = line.length;
          }
          modifiedLineNumber++;
        }
      }
    }

    return {
      code,
      addedLines: withDiffAddLineNumber,
      deletedLines: withDiffDeletedLineNumber,
      maxCharacterNumber,
    };
  }

  $addRuleFile(): void {
    this.getService(RulesService).$openProjectRules();
  }

  async clearTask() {
    const previousSessionId = this.sessionState.sessionId;
    this.resetTaskState();
    this.postComposerStateUpdate({
      localMessages: [],
      workspaceUri: "",
      sessionId: generateCustomUUID(),
      editingMessageTs: undefined,
      currentMessageTs: undefined,
    });
    if (previousSessionId) {
      const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
      kwaipilotBinaryModule.request("assistant/agent/local", {
        type: "stop",
        params: {
          sessionId: previousSessionId,
        },
      });
    }
  }

  private get editFile() {
    return this.getService(WriteToFileService);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  async $locateMentionNodeV2(node: MentionNodeV2Structure): Promise<void> {
    const openTextDocumentWithNotification = (uri: Uri): Thenable<TextDocument> => {
      return vscode.workspace.openTextDocument(uri).then(e => e, (e) => {
        vscode.window.showErrorMessage(`无法定位该文件${uri.toString()} , 请确认文件是否存在`);
        this.logger.error(`locateMentionNodeV2Failed:${uri.toString()}`, this.loggerScope, {
          err: e,
        });
        throw e;
      });
    };
    const openImageInEditor = (uri: Uri): Thenable<any> => {
      return vscode.commands.executeCommand("vscode.open", uri, this.viewColumn).then(
        result => result,
        (e) => {
          vscode.window.showErrorMessage(`无法打开图片文件 ${uri.toString()}, 请确认文件是否存在`);
          this.logger.error(`openImageFileFailed:${uri.toString()}`, this.loggerScope, {
            err: e,
          });
          throw e;
        },
      );
    };
    if (node.type === "remoteImage") {
      await openImageInEditor(vscode.Uri.parse(node.uri));
    }
    else if (node.type === "file" || node.type === "rule" || node.type === "remoteFile") {
      const doc = await openTextDocumentWithNotification(vscode.Uri.parse(node.uri));
      await vscode.window.showTextDocument(doc, this.viewColumn);
    }
    else if (node.type === "selection") {
      if (node.uri === "terminal://current") {
        vscode.commands.executeCommand("workbench.action.terminal.focus");
        return;
      }

      const doc = await openTextDocumentWithNotification(vscode.Uri.parse(node.uri));
      const editor = await vscode.window.showTextDocument(doc, this.viewColumn);
      const selectionStart = new vscode.Position(node.range.start.line, 0);
      const endLine = Math.min(node.range.end.line, doc.lineCount - 1);
      const selectionEnd = new vscode.Position(
        endLine,
        doc.lineAt(endLine).range.end.character,
      );
      editor.selections = [new vscode.Selection(selectionStart, selectionEnd)];

      // And the visible range jumps there too
      const range = new vscode.Range(selectionStart, selectionEnd);
      editor.revealRange(range);
    }
    else if (node.type === "tree" || node.type === "codebase" || node.type === "web" || node.type === "knowledge" || node.type === "slashCommand") {
      // 静默
    }
    else {
      const neverNode: never = node;
      throw new Error(`unsupported node type ${JSON.stringify(neverNode)}`);
    }
  }

  async $locateDiagnostic(uri: string, _diagnostic: SerializedDiagnostic): Promise<void> {
    const openTextDocumentWithNotification = (uri: Uri): Thenable<TextDocument> => {
      return vscode.workspace.openTextDocument(uri).then(e => e, (e) => {
        vscode.window.showErrorMessage(`无法定位该文件${uri.toString()} , 请确认文件是否存在`);
        this.logger.error(`locateDiagnosticFailed:${uri.toString()}`, this.loggerScope, {
          err: e,
        });
        throw e;
      });
    };

    const diagnostic = fromSerializedDiagnostic(_diagnostic);
    const doc = await openTextDocumentWithNotification(vscode.Uri.parse(uri));
    const editor = await vscode.window.showTextDocument(doc, this.viewColumn);
    editor.selections = [new vscode.Selection(diagnostic.range.start, diagnostic.range.end)];

    // And the visible range jumps there too
    editor.revealRange(diagnostic.range);
  }

  $setActiveSessionId(sessionId: string): void {
    this.getBase(WorkspaceStateManager).update(WorkspaceState.ACTIVE_COMPOSER_SESSION_ID, sessionId);
  }

  $getActiveSessionId(): string {
    return this.getBase(WorkspaceStateManager).get(WorkspaceState.ACTIVE_COMPOSER_SESSION_ID) || "";
  }

  $addFileToContext(uri: string): void {
    // 将 file:// 路径字符串转换为 vscode.Uri
    const fileUri = vscode.Uri.parse(uri);
    this.handleAddFileToContext(fileUri);
  }

  private get viewColumn() {
    return this.getBase(WorkspaceStateManager).get(WorkspaceState.FULL_MODE_NEW_WINDOW) ? vscode.ViewColumn.One : vscode.ViewColumn.Active;
  }
}
