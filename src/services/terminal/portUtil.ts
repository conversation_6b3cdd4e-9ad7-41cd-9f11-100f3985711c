import { exec } from "child_process";
import { LoggerManager } from "../../base/logger";

interface ProcessPortInfo {
  port: number;
  protocol: "TCP" | "UDP";
  state: string;
  address: string;
  pid?: number;
}

export class ProcessInfoManage {
  private loggerScope = "processInfoManage";
  // 性能优化配置
  private readonly FAST_TIMEOUT = 1000; // 快速超时：1秒

  constructor(private logger: LoggerManager) {}

  /**
     * 跨平台获取进程端口
     */
  private async getPortsByPid(pid: number): Promise<ProcessPortInfo[]> {
    const platform = process.platform;

    try {
      switch (platform) {
        case "win32":
          return await this.getPortsByPidWindows(pid);
        case "darwin":
          return await this.getPortsByPidMacOS(pid);
        case "linux":
          return await this.getPortsByPidLinux(pid);
        default:
          console.warn(`不支持的平台: ${platform}`);
          return [];
      }
    }
    catch (error) {
      console.error(`获取端口失败 (${platform}):`, error);
      return [];
    }
  }

  /**
     * 跨平台获取子进程
     */
  private async getChildProcesses(parentPid: number): Promise<number[]> {
    const platform = process.platform;

    try {
      switch (platform) {
        case "win32":
          return await this.getChildProcessesWindows(parentPid);
        case "darwin":
        case "linux":
          return await this.getChildProcessesUnix(parentPid);
        default:
          return [];
      }
    }
    catch (error) {
      console.error(`获取子进程失败:`, error);
      return [];
    }
  }

  /**
   * 检查进程是否有活跃的子进程（更可靠的命令完成检测）
   */
  async hasActiveChildProcesses(pid: number): Promise<boolean> {
    try {
      const children = await this.getChildProcesses(pid);
      this.logger?.info(`PID ${pid} 的子进程: ${JSON.stringify(children)}`, this.loggerScope);
      return children.length > 0;
    }
    catch (error: any) {
      this.logger?.info(`检查子进程失败 PID ${pid}: ${error.message}`, this.loggerScope);
      return false;
    }
  }

  private async getPortsByPidWindows(pid: number): Promise<ProcessPortInfo[]> {
    const ports: ProcessPortInfo[] = [];

    try {
      // 使用 netstat 获取端口信息
      const command = `netstat -ano | findstr ${pid}`;
      const output = await this.execCommand(command);

      const lines = output.split("\n");
      for (const line of lines) {
        const match = line.match(/^\s*(TCP|UDP)\s+([^:]+):(\d+)\s+[^:]+:\d+\s+(\w+)\s+(\d+)\s*$/);
        if (match && parseInt(match[5]) === pid) {
          ports.push({
            port: parseInt(match[3]),
            protocol: match[1] as "TCP" | "UDP",
            state: match[4],
            address: match[2],
            pid: pid,
          });
        }
      }
    }
    catch (error) {
      // Fallback: 使用 PowerShell
      try {
        const psCommand = `powershell "Get-NetTCPConnection | Where-Object {$_.OwningProcess -eq ${pid}} | Select-Object LocalPort,State,LocalAddress"`;
        const psOutput = await this.execCommand(psCommand);
        const psLines = psOutput.split("\n").slice(3); // 跳过标题行

        for (const line of psLines) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 3) {
            ports.push({
              port: parseInt(parts[0]),
              protocol: "TCP",
              state: parts[1],
              address: parts[2],
              pid: pid,
            });
          }
        }
      }
      catch (psError) {
        console.error("PowerShell fallback failed:", psError);
      }
    }

    return ports;
  }

  private async getChildProcessesWindows(parentPid: number): Promise<number[]> {
    try {
      // 使用 wmic 获取子进程
      const command = `wmic process where "ParentProcessId=${parentPid}" get ProcessId /format:csv`;
      const output = await this.execCommand(command);

      const childPids: number[] = [];
      const lines = output.split("\n");

      for (const line of lines) {
        const match = line.match(/,(\d+)/);
        if (match) {
          childPids.push(parseInt(match[1]));
        }
      }

      return childPids;
    }
    catch (error) {
      // Fallback: 使用 PowerShell
      try {
        const psCommand = `powershell "Get-WmiObject Win32_Process | Where-Object {$_.ParentProcessId -eq ${parentPid}} | Select-Object ProcessId"`;
        const output = await this.execCommand(psCommand);
        const lines = output.split("\n").slice(3); // 跳过标题

        return lines
          .map(line => parseInt(line.trim()))
          .filter(pid => !isNaN(pid));
      }
      catch (psError) {
        return [];
      }
    }
  }

  private async getPortsByPidMacOS(pid: number): Promise<ProcessPortInfo[]> {
    // 直接使用最精确的lsof方法，不使用不精确的grep fallback
    try {
      return await this.tryLsofMacOS(pid);
    }
    catch (error) {
      // 如果lsof失败，尝试使用ss命令（如果可用）
      return await this.tryAlternativeMacOS(pid);
    }
  }

  private async tryLsofMacOS(pid: number): Promise<ProcessPortInfo[]> {
    try {
      const command = `lsof -Pan -p ${pid} -i`;
      // 性能优化：使用快速超时
      const output = await this.execCommandFast(command);
      const ports = this.parseLsofOutput(output);
      if (ports.length > 0) {
        this.logger?.info(`lsof 检测到 PID ${pid} 的端口: ${JSON.stringify(ports)}`, this.loggerScope);
      }
      return ports;
    }
    catch (error: any) {
      // 减少日志噪音，lsof失败是常见情况
      return [];
    }
  }

  private async tryAlternativeMacOS(pid: number): Promise<ProcessPortInfo[]> {
    try {
      // 使用netstat + awk进行精确匹配，而不是简单的grep
      const command = `netstat -anv | awk '$9 == ${pid} {print $1, $4}' 2>/dev/null || true`;
      const output = await this.execCommandFast(command);

      if (!output.trim()) {
        return [];
      }

      const ports: ProcessPortInfo[] = [];
      const lines = output.split("\n").filter(line => line.trim());

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 2) {
          const protocol = parts[0];
          const address = parts[1];
          const match = address.match(/[.:](\d+)$/);
          if (match && (protocol === "tcp" || protocol === "udp")) {
            ports.push({
              port: parseInt(match[1]),
              protocol: protocol.toUpperCase() as "TCP" | "UDP",
              state: "LISTEN",
              address: address.split(/[.:]/)[0] || "0.0.0.0",
              pid: pid,
            });
          }
        }
      }

      return ports;
    }
    catch (error: any) {
      return [];
    }
  }

  // === Linux 实现 ===
  private async getPortsByPidLinux(pid: number): Promise<ProcessPortInfo[]> {
    const methods = [
      () => this.tryLsofLinux(pid),
      () => this.trySSLinux(pid),
      () => this.tryNetstatLinux(pid),
      () => this.tryProcNetLinux(pid),
    ];
    for (const method of methods) {
      try {
        const ports = await method();
        if (ports.length > 0) {
          return ports;
        }
      }
      catch (error) {
        continue;
      }
    }

    return [];
  }

  private async tryLsofLinux(pid: number): Promise<ProcessPortInfo[]> {
    const command = `lsof -Pan -p ${pid} -i`;
    const output = await this.execCommandFast(command);
    return this.parseLsofOutput(output);
  }

  private async trySSLinux(pid: number): Promise<ProcessPortInfo[]> {
    const command = `ss -tulpn | grep "pid=${pid}"`;
    const output = await this.execCommandFast(command);
    return this.parseSSOutput(output);
  }

  private async tryNetstatLinux(pid: number): Promise<ProcessPortInfo[]> {
    // 使用更精确的grep模式：确保PID在正确的列位置
    const command = `netstat -tulpn | awk '$7 ~ /^${pid}\\//'`;
    const output = await this.execCommandFast(command);
    return this.parseNetstatLinuxOutput(output);
  }

  private async tryProcNetLinux(pid: number): Promise<ProcessPortInfo[]> {
    // 直接读取 /proc/net/tcp 和 /proc/{pid}/fd/* 的方法
    return await this.readProcNetLinux(pid);
  }

  // === Unix 系统子进程查找 ===
  private async getChildProcessesUnix(parentPid: number): Promise<number[]> {
    const methods = [
      () => this.tryPgrepUnix(parentPid),
      () => this.tryPsUnix(parentPid),
    ];

    for (const method of methods) {
      try {
        const children = await method();
        if (children.length > 0) {
          return children;
        }
      }
      catch (error) {
        continue;
      }
    }

    return [];
  }

  private async tryPgrepUnix(parentPid: number): Promise<number[]> {
    const command = `pgrep -P ${parentPid}`;
    // 减少日志输出
    const output = await this.execCommandFast(command);

    return output.split("\n")
      .map(line => parseInt(line.trim()))
      .filter(pid => !isNaN(pid));
  }

  private async tryPsUnix(parentPid: number): Promise<number[]> {
    const command = process.platform === "darwin"
      ? `ps -axo pid,ppid | awk '$2 == ${parentPid} {print $1}'`
      : `ps -o pid= --ppid ${parentPid}`;
    // 减少日志输出
    const output = await this.execCommandFast(command);

    return output.split("\n")
      .map(line => parseInt(line.trim()))
      .filter(pid => !isNaN(pid));
  }

  // === 解析方法 ===
  private parseLsofOutput(output: string): ProcessPortInfo[] {
    const ports: ProcessPortInfo[] = [];
    const lines = output.split("\n");
    for (const line of lines) {
      if (line.trim() && !line.includes("COMMAND")) { // 跳过标题行
        // 修复：支持多种 lsof 输出格式
        const patterns = [
          // 标准格式: node 65332 user 21u IPv6 0x... 0t0 TCP *:3457 (LISTEN)
          /(\w+)\s+(\d+)\s+\w+\s+\d+u\s+(IPv4|IPv6)\s+\S+\s+\S+\s+(TCP|UDP)\s+([^:]+):(\d+)\s*(\(.*?\))?/,
          // 简化格式: TCP *:3457 (LISTEN)
          /(TCP|UDP)\s+([^:]+):(\d+)\s*(\(.*?\))?/,
          // 原有格式保持兼容
          /^(\w+)\s+\d+.*?\s(TCP|UDP)\s+(.+?):(\d+)\s*(\(.*?\))?\s*$/,
        ];

        for (const pattern of patterns) {
          const match = line.match(pattern);
          if (match) {
            let protocol, address, portStr, state;

            if (pattern === patterns[0]) {
              // 新格式: node 65332 user 21u IPv6 0x... 0t0 TCP *:3457 (LISTEN)
              [, , , , protocol, address, portStr, state] = match;
            }
            else if (pattern === patterns[1]) {
              // 简化格式: TCP *:3457 (LISTEN)
              [, protocol, address, portStr, state] = match;
            }
            else {
              // 原有格式
              [, , protocol, address, portStr, state] = match;
            }

            const port = parseInt(portStr);
            if (port > 0) {
              ports.push({
                port,
                protocol: protocol as "TCP" | "UDP",
                state: state ? state.replace(/[()]/g, "") : "UNKNOWN",
                address: address === "*" ? "0.0.0.0" : address,
              });

              this.logger?.info(`解析到端口: ${protocol} ${address}:${port} ${state || ""}`, this.loggerScope);
            }
            break; // 找到匹配的模式就停止尝试其他模式
          }
        }
      }
    }

    return ports;
  }

  private parseSSOutput(output: string): ProcessPortInfo[] {
    const ports: ProcessPortInfo[] = [];
    const lines = output.split("\n");

    for (const line of lines) {
      const match = line.match(/^(tcp|udp)\s+\w+\s+\d+\s+\d+\s+([^:]+):(\d+)/);
      if (match) {
        ports.push({
          port: parseInt(match[3]),
          protocol: match[1].toUpperCase() as "TCP" | "UDP",
          state: "LISTEN",
          address: match[2] === "*" ? "0.0.0.0" : match[2],
        });
      }
    }
    return ports;
  }

  private parseNetstatLinuxOutput(output: string): ProcessPortInfo[] {
    const ports: ProcessPortInfo[] = [];
    const lines = output.split("\n");

    for (const line of lines) {
      if (line.includes("LISTEN")) {
        const match = line.match(/(tcp|udp).*?([^:]+):(\d+)/);
        if (match) {
          ports.push({
            port: parseInt(match[3]),
            protocol: match[1].toUpperCase() as "TCP" | "UDP",
            state: "LISTEN",
            address: match[2] === "*" ? "0.0.0.0" : match[2],
          });
        }
      }
    }

    return ports;
  }

  private parseNetstatMacOSOutput(_output: string): ProcessPortInfo[] {
    // macOS netstat 解析实现
    return [];
  }

  private async readProcNetLinux(_pid: number): Promise<ProcessPortInfo[]> {
    // Linux /proc 文件系统读取实现
    return [];
  }

  // === 工具方法 ===
  private execCommand(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.logger?.info(`执行命令: ${command}`, this.loggerScope);
      exec(command, {
        timeout: 3000,
        encoding: "utf8",
      }, (error, stdout, stderr) => {
        if (error) {
          this.logger?.info(`命令执行失败: ${command}, 错误: ${error.message}`, this.loggerScope);
          if (stderr) {
            this.logger?.info(`命令标准错误输出: ${stderr}`, this.loggerScope);
          }
          reject(error);
        }
        else {
          this.logger?.info(`命令执行成功: ${command}, 输出长度: ${stdout.length}`, this.loggerScope);
          resolve(stdout);
        }
      });
    });
  }

  /**
     * 检查命令是否可用
     */
  async isCommandAvailable(command: string): Promise<boolean> {
    try {
      const checkCmd = process.platform === "win32"
        ? `where ${command}`
        : `which ${command}`;
      await this.execCommand(checkCmd);
      return true;
    }
    catch (error) {
      return false;
    }
  }

  /**
   * 递归获取进程及其所有子孙进程的信息
   */
  async getProcessInfoRecursive(rootPid: number): Promise<{
    pid: number;
    level: number;
    command?: string;
    ports: ProcessPortInfo[];
  }[]> {
    const results: any[] = [];
    const processedPids = new Set<number>();

    await this.findDescendantsRecursiveOptimized(rootPid, 0, results, processedPids);
    return results;
  }

  private async findDescendantsRecursiveOptimized(
    pid: number,
    level: number,
    results: any[],
    processedPids: Set<number>,
  ): Promise<void> {
    // 防止重复处理和无限递归
    if (processedPids.has(pid) || level > 10) {
      return;
    }

    processedPids.add(pid);

    try {
      // 优化：减少日志输出频率
      if (level === 0) {
        this.logger.info(`开始处理进程树 PID ${pid}`, this.loggerScope);
      }

      // 并行执行所有查询
      const [command, ports, children] = await Promise.all([
        this.getProcessCommand(pid).catch(() => undefined),
        this.getPortsByPid(pid).catch(() => []),
        this.getChildProcesses(pid).catch(() => []),
      ]);

      results.push({
        pid,
        level,
        command,
        ports,
      });

      // 如果有子进程，并行处理所有子进程
      if (children.length > 0) {
        const childPromises = children.map(childPid =>
          this.findDescendantsRecursiveOptimized(childPid, level + 1, results, processedPids),
        );
        await Promise.all(childPromises);
      }

      // 减少日志输出
      if (ports.length > 0) {
        this.logger.info(`PID ${pid} 检测到端口: ${ports.map(p => p.port).join(",")}`, this.loggerScope);
      }
    }
    catch (error: any) {
      // 只记录关键错误
      if (level === 0) {
        this.logger.info(`处理进程树失败 PID ${pid}: ${error.message}`, this.loggerScope);
      }
    }
  }

  private async getProcessCommand(pid: number): Promise<string | undefined> {
    try {
      const command = process.platform === "win32"
        ? `wmic process where "ProcessId=${pid}" get CommandLine /format:csv`
        : `ps -p ${pid} -o command=`;

      const output = await this.execCommandFast(command);
      let result: string | undefined;

      if (process.platform === "win32") {
        const lines = output.split("\n");
        for (const line of lines) {
          if (line.includes(",")) {
            result = line.split(",")[1]?.trim();
            break;
          }
        }
      }
      else {
        result = output.trim();
      }

      return result;
    }
    catch (error) {
      return undefined;
    }
  }

  // 快速执行命令版本
  private execCommandFast(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      // 减少日志输出，只记录失败的命令
      exec(command, {
        timeout: this.FAST_TIMEOUT, // 使用更短的超时时间
        encoding: "utf8",
      }, (error, stdout, _stderr) => {
        if (error) {
          // 区分超时错误和无结果错误
          if (error.code === 124) {
            this.logger.info(`命令超时: ${command.split(" ")[0]}, 超过${this.FAST_TIMEOUT}ms`, this.loggerScope);
          }
          else if (error.code && error.code > 0) {
            // 命令执行成功但返回非0退出码（通常表示没找到结果）
            // 这是正常情况，不记录错误日志
          }
          else {
            // 真正的命令执行错误（如命令不存在）
            this.logger.info(`命令执行错误: ${command.split(" ")[0]}, ${error.message}`, this.loggerScope);
          }
          reject(error);
        }
        else {
          resolve(stdout);
        }
      });
    });
  }
}
