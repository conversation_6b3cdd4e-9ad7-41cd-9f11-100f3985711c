import { IPty } from "node-pty";
import { LoggerManager } from "../../base/logger";
import { Sleep } from "../../utils";
import { TerminalStatus } from ".";
import * as vscode from "vscode";
import * as os from "node:os";
import { ProcessInfoManage } from "./portUtil";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { WebviewContext } from "shared/lib/bridge/protocol";
import stripAnsi from "strip-ansi";
import { createDeferred } from "shared/lib/util";
import { WebloggerManager } from "../../base/weblogger";

function loadNativeModule<T>(id: string): T | null {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return require(`${vscode.env.appRoot}/node_modules.asar/${id}`);
  }
  catch (err) {
    // ignore
  }

  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return require(`${vscode.env.appRoot}/node_modules/${id}`);
  }
  catch (err) {
    // ignore
  }

  return null;
}
const pty = loadNativeModule<typeof import("node-pty")>("node-pty");

export class Terminal extends vscode.Disposable {
  status: TerminalStatus = "idle";
  private loggerScope = "terminal";
  private terminal: vscode.Terminal;
  private rawOutput: string = "";

  private writeEmitter: vscode.EventEmitter<string> = new vscode.EventEmitter<string>();
  private ptyProcess: IPty;
  private isInitialized = false;
  private initDeferred = createDeferred<void>();

  private runStartTimestamp = 0;

  private notWriteOutput = "";
  // private writeQueue = new PQueue({ concurrency: 1 }); // 确保按顺序处理

  private BLOCK_TASK_CHECK_COUNT = 6; //
  // NOTE: 动态变化的，所以不定义，总超时时间为 3s => 100 + 250 * 2 + 400 * 2 + 800 * 2

  // 总共超时5min
  private NON_BLOCK_TASK_CHECK_COUNT = 1000; // 1 second
  private NON_BLOCK_TASK_CHECK_INTERVAL = 300; // 300ms

  private readonly MAX_OUTPUT_SIZE = 1024 * 1024; // 1MB
  private commandEndTag: string = "2aMuqspkaOVljau-expB7C";

  // 获取ui展示的行数，在命令执行之前，执行结束后获取本次命令的结果
  private UITerminalLinenumber = 0;
  // 终端的创建一定是 runCommand导致的

  private addToRawOutput(data: string) {
    this.rawOutput += data;
    if (this.rawOutput.length > this.MAX_OUTPUT_SIZE) {
    // 保留最后一部分输出
      this.rawOutput = this.rawOutput.slice(-this.MAX_OUTPUT_SIZE / 2);
    }
  }

  constructor(private logger: LoggerManager, private processInfoManage: ProcessInfoManage, private rpcContext: IRPCProtocol, private weblog: WebloggerManager, private callbacks?: {
    onClose: (pid: number) => void;
  }) {
    super(() => this.dispose());
    const env = { ...(process.env as any) };
    if (os.platform() !== "win32") {
      env.PATH += `:${["/opt/homebrew/bin", "/opt/homebrew/sbin"].join(":")}`;
      env.LANG = "zh_CN.UTF-8";
      env.LC_ALL = "zh_CN.UTF-8";
      env.LC_CTYPE = "zh_CN.UTF-8";
    }

    // Create the pseudo terminal
    this.ptyProcess = pty!.spawn(this.shellCmd, [], {
      name: "xterm-256color",
      cols: 80,
      rows: 26,
      cwd: this.rootDir,
      env,
      useConpty: true,
      encoding: "utf8",
    });

    process.on("exit", () => {
      this.ptyProcess.kill();
    });
    const newPty: vscode.Pseudoterminal = {
      onDidWrite: this.writeEmitter.event,
      open: () => {
        this.injectCommand();
      },
      close: () => {
        this.callbacks?.onClose?.(this.pid);
      },
      handleInput: (data) => {
        this.ptyProcess.write(data);
      },
    };
    this.terminal = vscode.window.createTerminal({
      name: "Kwaipilot",
      pty: newPty,
      iconPath: new vscode.ThemeIcon("robot"),
    });
    // this.setupProcessEventListeners()
  }

  private async injectCommand() {
    await Sleep(2000);
    this.terminal.sendText(this.commandEncCommand);
    await Sleep(100);
    this.initDeferred.resolve();
    this.isInitialized = true;
    this.startDataListener();
  }

  private startDataListener() {
    this.ptyProcess.onData((originalData: string) => {
      this.logger.info(`[Terminal] Received  ${originalData}`, this.loggerScope);
      const ignoreAnsi = stripAnsi(originalData).trim() === "";
      let data = originalData;
      // 去除添加的标记
      if (!ignoreAnsi) {
        const isIdel = this.commandExecuteEnd(data);
        if (isIdel) {
          this.status = "idle";
          data = data.replace(this.commandEndTag, "");
        }
      }

      this.addToRawOutput(data);
      this.rpcContext.getProxy(WebviewContext.WebviewTerminal).$write(this.pid, data);

      this.writeEmitter.fire(data);
    });
  }

  moveToEditor() {
    this.terminal.show();
    vscode.commands.executeCommand("workbench.action.terminal.moveToEditor");
  }

  async runCommand(command: string, is_background: boolean): Promise<string> {
    if (!this.isInitialized) {
      await this.initDeferred.promise;
    }
    // 获取命令执行前的行号作为起始位置
    this.UITerminalLinenumber = await this.rpcContext.getProxy(WebviewContext.WebviewTerminal).$getCurrentLinenumber(this.pid);

    await this.rpcContext.getProxy(WebviewContext.WebviewTerminal).$renderUiTerminal(this.pid);
    this.runStartTimestamp = Date.now();
    this.terminal.sendText(command);
    this.status = "busy";
    return await this.waitForCommandToFinish(is_background);
  }

  private async waitForCommandToFinish(is_background: boolean): Promise<string> {
    return new Promise<string>((resolve, _reject) => {
      let isResolved = false; // 添加标志防止重复 resolve

      const safeResolve = (value: string) => {
        if (!isResolved) {
          if (is_background) {
            this.status = "background";
          }
          isResolved = true;
          this.weblog.$reportUserAction({
            key: "command_run",
            type: is_background ? "background" : "normal",
            content: JSON.stringify({
              run_time: Date.now() - this.runStartTimestamp,
            }),
          });
          resolve(value);
        }
      };

      if (is_background) {
        // 对于后台命令，使用递归的方式检测服务启动
        this.checkBackgroundTaskRecursively(safeResolve, 0, () => isResolved);
      }
      else {
        // 对于前台命令，使用简单的递归检查状态
        this.checkForegroundTaskRecursively(safeResolve, 0, () => isResolved);
      }
    });
  }

  private checkBackgroundTaskRecursively(resolve: (value: string) => void, checkCount: number, isResolved?: () => boolean): void {
    // 如果已经 resolved，停止执行
    if (isResolved && isResolved()) {
      return;
    }

    checkCount++;

    Sleep(100).then(async () => {
      (async () => {
        try {
        // 再次检查是否已经 resolved
          if (isResolved && isResolved()) {
            return;
          }

          // 递归检查所有子孙进程的端口 - 性能优化后的版本
          const childrens = await this.processInfoManage.getProcessInfoRecursive(this.pid);

          // 检查是否在异步操作期间 resolved
          if (isResolved && isResolved()) {
            return;
          }

          const portsInfo = [];
          for (const child of childrens) {
            portsInfo.push(...child.ports);
          }

          // 只在第一次和检测到端口时记录
          if (checkCount === 1 || portsInfo.length > 0) {
            this.logger.info(
              `后台任务检查 ${checkCount}/${this.BLOCK_TASK_CHECK_COUNT}: 端口=[${portsInfo.map(p => p.port).join(",")}]`,
              this.loggerScope,
            );
          }

          if (portsInfo.length > 0) {
            this.logger.info(`后台服务已启动，监听端口: ${portsInfo.map(p => p.port).join(",")}`, this.loggerScope);
            resolve(await this.getCommandOutput());
            return;
          }

          if (checkCount >= this.BLOCK_TASK_CHECK_COUNT) {
            this.logger.info(`后台任务检查超时，返回当前输出`, this.loggerScope);
            resolve(await this.getCommandOutput());
            return;
          }
        }
        catch (error: any) {
          if (isResolved && isResolved()) {
            return;
          }

          if (checkCount >= this.BLOCK_TASK_CHECK_COUNT) {
            this.logger.info(`后台任务检查出错: ${error.message}`, this.loggerScope);
            resolve(await this.getCommandOutput());
            return;
          }
        }
        finally {
        // 只有在未 resolved 时才继续递归调用
          if (!isResolved || !isResolved()) {
          // 性能优化：动态调整检查间隔，第一次更快响应
            const delay = checkCount < 3 ? 250 : (checkCount < 5 ? 400 : 800);
            setTimeout(() => {
              this.checkBackgroundTaskRecursively(resolve, checkCount, isResolved);
            }, delay);
          }
        }
      })();
    });
  }

  private checkForegroundTaskRecursively(resolve: (value: string) => void, checkCount: number, isResolved?: () => boolean): void {
    // 如果已经 resolved，停止执行
    if (isResolved && isResolved()) {
      return;
    }

    checkCount++;
    Sleep(100).then(async () => {
      try {
        if (isResolved && isResolved()) {
          return;
        }
        if (this.status === "idle") {
          this.logger.info(`前台命令执行完成`, this.loggerScope);
          resolve(await this.getCommandOutput());
          return;
        }

        if (checkCount > this.NON_BLOCK_TASK_CHECK_COUNT) {
          this.logger.info(`前台命令执行超时`, this.loggerScope);
          resolve(await this.getCommandOutput());
          return;
        }
      }
      catch (error: any) {
        if (isResolved && isResolved()) {
          return;
        }
      }
      finally {
        if (!isResolved || !isResolved()) {
          setTimeout(() => {
            this.checkForegroundTaskRecursively(resolve, checkCount, isResolved);
          }, this.NON_BLOCK_TASK_CHECK_INTERVAL);
        }
      }
    });
  }

  private commandExecuteEnd(line: string): boolean {
    return line.includes(this.commandEndTag);
  }

  dispose() {
    this.writeEmitter.dispose();
    // this.writeQueue.clear(); // 清空队列
    this.ptyProcess.kill();
    this.terminal.dispose();
  }

  async getCommandOutput(): Promise<string> {
    return await this.rpcContext.getProxy(WebviewContext.WebviewTerminal).$getTerminalContent(this.pid, this.UITerminalLinenumber);
  }

  isClosed(): boolean {
    return this.terminal.exitStatus !== undefined;
  }

  write(data: string) {
    if (data.endsWith("\r")) {
      this.status = "busy";
    }
    this.ptyProcess.write(data);
  }

  show() {
    this.terminal.show();
  }

  get pid() {
    return this.ptyProcess.pid;
  }

  private get shellCmd(): string {
    if (process.platform !== "win32") {
      return os.userInfo().shell || "cmd";
    }
    switch (process.platform) {
      case "win32":
        return process.env.COMSPEC || "cmd.exe";
    }
  }

  private get rootDir(): string {
    const isWindows = os.platform() === "win32";
    let cwd = isWindows ? process.env.USERPROFILE : process.env.HOME;
    if (
      vscode.workspace.workspaceFolders
      && vscode.workspace.workspaceFolders.length > 0
    ) {
      cwd = vscode.workspace.workspaceFolders[0].uri.fsPath;
    }
    return cwd || "/";
  }

  private get commandEncCommand(): string {
    if (this.shellCmd.includes("zsh")) {
      const uniqueId = Math.random().toString(36).substring(2, 8);
      // 将多行命令转换为单行格式，同时保持功能不变
      return `function kwaipilot_precmd_${uniqueId}() { echo "${this.commandEndTag}"; }; autoload -Uz add-zsh-hook 2>/dev/null; add-zsh-hook precmd kwaipilot_precmd_${uniqueId} 2>/dev/null`;
    }
    if (this.shellCmd.includes("bash")) {
      const uniqueId = Math.random().toString(36).substring(2, 8);

      return `if [ -n "$PROMPT_COMMAND" ]; then export KWAIPILOT_OLD_PROMPT_COMMAND_${uniqueId}="$PROMPT_COMMAND"; fi; function kwaipilot_prompt_${uniqueId}() { echo "${this.commandEndTag}"; }; export PROMPT_COMMAND="kwaipilot_prompt_${uniqueId}; \${KWAIPILOT_OLD_PROMPT_COMMAND_${uniqueId}:-:}"`;
    }
    return `export PS1="$PS1\\$(echo '${this.commandEndTag}')"`;
  }
}
