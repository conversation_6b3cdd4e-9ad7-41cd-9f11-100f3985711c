/* Terminal emulator - commented because node-pty is causing problems. */

import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";

import { ExtensionTerminalShape } from "shared/lib/bridge/protocol";
import { Webview } from "@webview";
import { LoggerManager } from "../../base/logger";
import { Terminal } from "./terminal";
import { ProcessInfoManage } from "./portUtil";
import { CommandStatusCheckResponse } from "shared/lib/agent";
import { Sleep } from "../../utils";
import { WebloggerManager } from "../../base/weblogger";

export class TerminalManager extends ServiceModule implements ExtensionTerminalShape {
  private loggerScope = "terminalManager";
  private mainTerminal: Terminal | null = null;
  private backupTerminal: Terminal | null = null;
  private terminals = new Map<number, Terminal>();
  private processInfoManage = new ProcessInfoManage(this.logger);
  private isDisposed = false;

  $showTerminal(pid?: number) {
    this.ensureMainTerminal();
    const ap = pid || this.mainTerminal?.pid || -1;
    this.terminals.get(ap)?.show();
  }

  $writeToTerminal(pid: number, data: string): void {
    this.terminals.get(pid)?.write(data);
  }

  $disposeTerminalIfNotIsBackground() {
    const pids: number[] = [];
    this.terminals.forEach((terminal, pid) => {
      if (terminal.status !== "background") {
        terminal.dispose();
        pids.push(pid);
        this.terminals.delete(pid);
      }
    });
    return pids;
  }

  async getCommandCheckResultWithDelay(delayS: number): Promise<CommandStatusCheckResponse> {
    this.ensureMainTerminal();
    if (!this.mainTerminal || this.mainTerminal.status !== "background") {
      this.logger.info(`当前主进程状态为非background， 跳过检查`, this.loggerScope);
      return {
        status: "error",
        output: "当前主进程没有在执行background命令，不需要检查",
      };
    }
    else {
      await Sleep(delayS * 1000);
      const output = await this.mainTerminal.getCommandOutput();
      this.logger.info(`检查命令结果：${output}`, this.loggerScope);
      return {
        status: "success",
        output,
      };
    }
  }

  async runCommand(command: string, is_background: boolean): Promise<string> {
    this.ensureMainTerminal();
    if (!this.mainTerminal) {
      throw new Error("无法创建主终端");
    }

    const isIdle = this.mainTerminal.status === "idle";
    if (!isIdle) {
      this.changtMain();
    }
    return this.mainTerminal.runCommand(command, is_background);
  }

  private changtMain() {
    this.ensureBackupTerminal();
    if (!this.backupTerminal) {
      throw new Error("无法创建备用终端");
    }

    this.mainTerminal = this.backupTerminal;
    this.terminals.set(this.mainTerminal.pid, this.mainTerminal);
    this.backupTerminal = this.createTerminal();
  }

  private IMMain(pid: number) {
    return this.mainTerminal?.pid === pid;
  }

  constructor(
    ctx: ContextManager,
  ) {
    super(ctx);
    // this.mainTerminal = this.createTerminal();
    // if (this.mainTerminal) {
    //   this.terminals.set(this.mainTerminal.pid, this.mainTerminal);
    // }
    // this.backupTerminal = this.createTerminal();

    // this.listenProcessMessage();
  }

  $getPtyProcessID() {
    this.ensureMainTerminal();
    return this.mainTerminal?.pid || -1;
  }

  async getProcessInfo(pid?: number) {
    if (pid) {
      return [await this.processInfoManage.getProcessInfoRecursive(pid)];
    }
    const processInfo = [];
    for (const pid of this.terminals.keys()) {
      const p = await this.processInfoManage.getProcessInfoRecursive(pid);
      processInfo.push(p);
    }
    return processInfo;
  }

  private onClosed(pid: number) {
    this.logger.info(`终端进程 ${pid} 已关闭`, this.loggerScope);

    // 检查是否是主终端被关闭
    if (this.mainTerminal && this.mainTerminal.pid === pid) {
      this.logger.warn(`主终端 ${pid} 被外部关闭，将重新创建`, this.loggerScope);
      this.mainTerminal = null;
    }

    // 检查是否是备份终端被关闭
    if (this.backupTerminal && this.backupTerminal.pid === pid) {
      this.logger.warn(`备份终端 ${pid} 被外部关闭，将重新创建`, this.loggerScope);
      this.backupTerminal = null;
    }

    // 从终端映射中移除
    this.terminals.delete(pid);
  }

  private listenProcessMessage() {
    const check = async () => {
      // const ports: number[] = [];
      // for (const pid of this.terminals.keys()) {
      //   const p = await this.processInfoManage.getProcessInfoRecursive(pid);
      //   p.forEach((info) => {
      //     ports.push(...info.ports.map(ps => ps.port));
      //   });
      // }
      const res = await this.getProcessInfo();

      this.logger.info(`当前端口信息${JSON.stringify(res)}`, this.loggerScope);
      setTimeout(check, 10000);
    };
    check();
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get rpcContext() {
    return this.getBase(Webview).rpcContext;
  }

  /**
   * 创建新的终端实例
   */
  private createTerminal(): Terminal | null {
    if (this.isDisposed) {
      this.logger.warn("TerminalManager已被销毁，无法创建新终端", this.loggerScope);
      return null;
    }

    try {
      const terminal = new Terminal(
        this.logger,
        this.processInfoManage,
        this.rpcContext,
        this.getBase(WebloggerManager),
        { onClose: pid => this.onClosed(pid) },
      );
      this.logger.info(`创建新终端，PID: ${terminal.pid}`, this.loggerScope);
      return terminal;
    }
    catch (error) {
      this.logger.error(`创建终端失败: ${error}`, this.loggerScope);
      return null;
    }
  }

  /**
   * 确保主终端可用
   */
  private ensureMainTerminal(): void {
    if (!this.mainTerminal || this.isTerminalClosed(this.mainTerminal)) {
      this.logger.info("主终端不可用，重新创建", this.loggerScope);

      // 如果原来的主终端存在且已关闭，从映射中移除
      if (this.mainTerminal) {
        this.terminals.delete(this.mainTerminal.pid);
      }

      this.mainTerminal = this.createTerminal();
      if (this.mainTerminal) {
        this.terminals.set(this.mainTerminal.pid, this.mainTerminal);
      }
    }
  }

  /**
   * 确保备份终端可用
   */
  private ensureBackupTerminal(): void {
    if (!this.backupTerminal || this.isTerminalClosed(this.backupTerminal)) {
      this.logger.info("备份终端不可用，重新创建", this.loggerScope);

      // 如果原来的备份终端存在且已关闭，从映射中移除
      if (this.backupTerminal) {
        this.terminals.delete(this.backupTerminal.pid);
      }

      this.backupTerminal = this.createTerminal();
    }
  }

  /**
   * 检查终端是否已关闭
   */
  private isTerminalClosed(terminal: Terminal): boolean {
    try {
      return terminal.isClosed();
    }
    catch (error) {
      // 如果调用isClosed时出错，说明终端已经无效
      this.logger.warn(`检查终端状态时出错，视为已关闭: ${error}`, this.loggerScope);
      return true;
    }
  }

  /**
   * 销毁管理器
   */
  dispose(): void {
    this.isDisposed = true;

    // 销毁所有终端
    this.terminals.forEach((terminal) => {
      try {
        terminal.dispose();
      }
      catch (error) {
        this.logger.error(`销毁终端时出错: ${error}`, this.loggerScope);
      }
    });

    this.terminals.clear();
    this.mainTerminal = null;
    this.backupTerminal = null;

    this.logger.info("TerminalManager已销毁", this.loggerScope);
  }
}

export type TerminalStatus = "idle" | "busy" | "background";
