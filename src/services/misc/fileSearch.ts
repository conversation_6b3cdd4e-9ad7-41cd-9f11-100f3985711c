import { FileSearchResult, FileSearchMatch, SearchResultItem } from "shared/lib/bridge/protocol";
import { spawnRipgrepCmd } from "./ripgrepFileSearch";
import * as vscode from "vscode";
import * as path from "path";

/**
 * 文件名匹配函数
 * @param fileName 文件名
 * @param query 搜索词
 * @returns 是否匹配
 */
function isFileNameMatch(fileName: string, query: string): boolean {
  // TODO: 未来可以替换为更高级的模糊匹配算法，如 fuzzy-search
  return fileName.toLowerCase().includes(query.toLowerCase());
}

/**
 * 处理搜索结果
 * @param line 文件路径
 * @param query 搜索词
 * @param cwd 工作目录
 * @returns 如果匹配返回 URI，否则返回 null
 */
function processSearchResult(line: string, query: string, cwd: string): SearchResultItem | null {
  const fileName = path.basename(line);
  if (isFileNameMatch(fileName, query)) {
    const uri = vscode.Uri.file(path.join(cwd, line));
    const relativePath = vscode.workspace.asRelativePath(uri);

    // 计算高亮范围
    const lowerFileName = fileName.toLowerCase();
    const lowerQuery = query.toLowerCase();
    const startIndex = lowerFileName.indexOf(lowerQuery);

    const highlights: FileSearchMatch[] = [{
      start: startIndex,
      end: startIndex + query.length,
    }];

    return {
      uri: uri.toJSON(),
      label: fileName,
      highlights: { label: highlights },
      relativePath,
    };
  }
  return null;
}

/**
 * 处理目录搜索结果
 * @param dir 目录路径
 * @param query 搜索词
 * @param cwd 工作目录
 * @returns 如果匹配返回目录的 URI，否则返回 null
 */
function processDirectorySearchResult(dir: string, query: string, cwd: string): SearchResultItem | null {
  const dirName = path.basename(dir);
  if (isFileNameMatch(dirName, query)) {
    const uri = vscode.Uri.file(path.join(cwd, dir));
    const relativePath = vscode.workspace.asRelativePath(uri);

    // 计算高亮范围
    const lowerDirName = dirName.toLowerCase();
    const lowerQuery = query.toLowerCase();
    const startIndex = lowerDirName.indexOf(lowerQuery);

    const highlights: FileSearchMatch[] = [{
      start: startIndex,
      end: startIndex + query.length,
    }];

    return {
      uri: uri.toJSON(),
      label: dirName,
      highlights: { label: highlights },
      relativePath,
    };
  }
  return null;
}

export async function doFileSearch(query: string, limit: number): Promise<FileSearchResult> {
  const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!cwd) {
    return {
      results: [],
    };
  }

  return new Promise<FileSearchResult>((resolve, reject) => {
    const results: SearchResultItem[] = [];
    let isResolved = false;
    let errorOutput = "";

    spawnRipgrepCmd(cwd).then(({ cmd }) => {
      cmd.stdout.on("data", (data: Buffer) => {
        if (isResolved) return;

        const lines = data.toString().split("\n").filter(Boolean);
        for (const line of lines) {
          const result = processSearchResult(line, query, cwd);
          if (result) {
            results.push(result);

            if (results.length >= limit) {
              isResolved = true;
              cmd.kill();
              resolve({
                results: results.slice(0, limit),
              });
              break;
            }
          }
        }
      });

      cmd.stderr.on("data", (data: Buffer) => errorOutput += data.toString());

      cmd.on("close", (code: number) => {
        if (!isResolved) {
          // ripgrep exit codes:
          // 0: 找到匹配
          // 1: 没有找到匹配（正常情况）
          // 2: 发生错误
          if (code === 2 || code > 2) {
            reject(new Error(`ripgrep search failed:\n${errorOutput} ${code}`));
          }
          else {
            // code 0 或 1 都是正常情况
            resolve({
              results,
            });
          }
        }
      });

      cmd.on("error", (err: Error) => {
        if (!isResolved) {
          reject(err);
        }
      });
    }).catch(reject);
  });
}

function listAncestorDirectories(dirPath: string): string[] {
  const dirs: string[] = [];
  let currentPath = dirPath;
  while (currentPath !== "." && currentPath !== "" && currentPath !== "/") {
    dirs.push(currentPath);
    currentPath = path.dirname(currentPath);
  }
  return dirs;
}

export async function doDirectorySearch(query: string, limit: number): Promise<FileSearchResult> {
  const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!cwd) {
    return {
      results: [],
    };
  }

  return new Promise<FileSearchResult>((resolve, reject) => {
    const resultsMap = new Map<string, SearchResultItem>();
    let isResolved = false;
    let errorOutput = "";

    spawnRipgrepCmd(cwd).then(({ cmd }) => {
      cmd.stdout.on("data", (data: Buffer) => {
        if (isResolved) return;

        const lines = data.toString().split("\n").filter(Boolean);
        for (const relativePath of lines
          .map(line => path.dirname(line))
          .flatMap(listAncestorDirectories)) {
          // 遍历所有父级目录
          if (resultsMap.has(relativePath)) {
            continue;
          }
          const result = processDirectorySearchResult(relativePath, query, cwd);
          if (result) {
            resultsMap.set(relativePath, result);

            if (resultsMap.size >= limit) {
              isResolved = true;
              cmd.kill();
              resolve({
                results: Array.from(resultsMap.values()).slice(0, limit),
              });
              break;
            }
          }
          if (isResolved) break;
        }
      });

      cmd.stderr.on("data", (data: Buffer) => errorOutput += data.toString());

      cmd.on("close", (code: number) => {
        if (!isResolved) {
          if (code === 2 || code > 2) {
            reject(new Error(`ripgrep search failed:\n${errorOutput} ${code}`));
          }
          else {
            resolve({
              results: Array.from(resultsMap.values()),
            });
          }
        }
      });

      cmd.on("error", (err: Error) => {
        if (!isResolved) {
          reject(err);
        }
      });
    }).catch(reject);
  });
}
