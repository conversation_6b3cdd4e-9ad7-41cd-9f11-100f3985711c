import { getBinPath } from "vscode-ripgrep-utils";
import * as vscode from "vscode";
import { spawn } from "child_process";

export async function spawnRipgrepCmd(cwd: string) {
  const rgArgs = getRgArgs();
  const rgDiskPath = await getBinPath(vscode.env.appRoot);
  if (!rgDiskPath) {
    throw new Error("ripgrep not found");
  }
  return {
    cmd: spawn(rgDiskPath, rgArgs.args, { cwd }),
    rgDiskPath,
    rgArgs,
    cwd,
  };
}

function getRgArgs() {
  const args = [
    "--files", "--hidden", "--case-sensitive", "--no-require-git",
    "--no-config",
    "-g",
    // TODO: respect vscode file exclude config
    "!**/{node_modules,.git,.github,out,dist,__pycache__,.venv,.env,venv,env,.cache,tmp,temp}/**",
  ];

  return {
    args,
  };
}
