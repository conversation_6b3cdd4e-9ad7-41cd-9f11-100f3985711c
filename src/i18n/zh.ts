export default {
  // Login related
  "loginRequired": "Kwaipilot：请在登录后使用相关功能！",
  "loginSuccessful": "登录成功！",
  "logoutSuccessful": "注销成功！",
  "login": "登录",
  "dontRemindAgain": "不再提示",

  // Inline chat
  "failedToInitializeInlineChat": "初始化内联聊天消息处理器失败",
  "conversationalCodeGeneration": "⌘+I 对话式生成代码",
  "close": "关闭",
  "inlineTip": "行内提示",
  "conversationalCodeGenerationTipDisabled": "对话式生成代码提示已被禁用。",

  // Code actions
  "functionComment": "函数注释",
  "clickToGenerateFunctionComment": "点击生成函数注释",
  "lineComment": "行间注释",
  "clickToGenerateLineComment": "点击生成行间注释",
  "codeExplanation": "代码解释",
  "clickToExplainCode": "点击进行代码解释",
  "functionSplit": "函数拆分",
  "clickToSplitFunction": "点击进行函数拆分",
  "tuningSuggestion": "调优建议",
  "clickToGetTuningSuggestions": "点击进行调优建议",

  // Theme errors
  "colorThemeConfigError": "颜色主题配置错误：'colors' 属性不是对象类型",
  "errorLoadingThemeSettings": "加载主题设置错误",
  "errorParsingTheme": "解析主题错误",

  // General errors
  "payloadRequired": "payload 不能为空",
  "failedToPassMessageToLocalService": "透传消息到local-service失败: {{msg}}",

  // Rules
  "ruleConfigurationGuide": "规则配置说明",
  "ruleExampleDescription": "填写示例：1.在生成代码时添加函数级注释。 注意：本文件最多不超过5000个字符。hover文本可点击\"规则配置说明\"跳转查看具体说明。",
  "failedToUpdateRuleFileList": "更新规则文件列表失败: {{msg}}",
  "failedToOpenUserRuleFile": "打开用户规则文件失败: {{msg}}",
  "pleaseOpenProjectFirst": "请先打开一个项目",
  "onlyLowercaseSupported": "仅支持英文小写字母、数字、- 和 _",
  "maxFileNameLength": "文件名最大长度为100",
  "pleaseEnterRuleFileName": "请输入规则文件名",
  "ruleConfigurationInstructions": "若在每次对话时均需要触发此规则，请将上方的\"alwaysApply: false\"中的false改为true。\n  如本规则不需要跟随项目进行commit，请在.gitignore文件中添加该规则文件。\n  查看[规则配置说明](https://docs.corp.kuaishou.com/d/home/<USER>",
  "failedToOpenProjectRuleFile": "打开项目规则文件失败: {{msg}}",

  // Annotation
  "currentSelectedCodeAnnotated": "当前选中代码已被标注，请选择其他代码块",
  "selectedCodeBlockHasAnnotation": "选中代码块已有标注，请重新框选",

  // Settings
  "kwaipilotSetting": "Kwaipilot 设置",

  // Index file operations
  "confirmDeleteIndex": "删除代码索引将影响仓库代码问答准确性，确定删除代码索引？",
  "confirmDeleteIndexButton": "确认",
  "noWorkspaceFolder": "未找到工作区文件夹",
  "indexIgnoreFileContent": "# 添加需要忽略构建索引的文件目录或文件类型（例如 bin/a.js 或者 *.csv），并通过换行分隔；\n# 如需仅构建指定目录如/src，可以使用 * 和 !/src 的组合",
  "notifyWebview": "通知webview",
  "updatingIndex": "更新索引中... {{progress}}%",
  "indexBuildPaused": "索引构建已暂停...",

  // File upload operations
  "fileSizeExceedsLimit": "上传文件总大小超过 3MB 上限，请调整后重试",
  "imageCountExceedsLimit": "添加失败，最多添加 10 张图片",

  // handleAddFileToContext
  "handleAddFileToContext.fileNotFound": "handleAddFileToContext: 未找到要添加的文件",
  "handleAddFileToContext.notFileOrDirectory": "handleAddFileToContext: {{path}} 不是一个文件/目录",
  "handleAddFileToContext.notFileOrDirectoryMessage": "{{path}} 不是一个文件/目录",
  "handleAddFileToContext.failedToAdd": "无法添加文件/目录到智能体上下文: {{path}}",

  // locateByPath
  "locateByPath.failedToLocate": "无法定位该文件{{path}}，请确认文件是否存在",

  // locateMentionNodeV2
  "locateMentionNodeV2.failedToLocate": "无法定位该文件{{path}}，请确认文件是否存在",
  "locateMentionNodeV2.failedToOpenImage": "无法打开图片文件 {{path}}，请确认文件是否存在",

  // locateDiagnostic
  "locateDiagnostic.failedToLocate": "无法定位该文件{{path}}，请确认文件是否存在",

  // executeCommand
  "executeCommand.commandFailed": "命令运行失败: {{error}}",

  // 日志相关消息
  "log.directoryNotExists": "日志目录不存在",
  "log.cannotGeneratePackage": "日志目录不存在，无法生成日志包",
  "log.generatingPackage": "正在生成日志包...",
  "log.filteringFiles": "正在筛选日志文件...",
  "log.noFilesToPackage": "没有找到需要打包的日志文件",
  "log.compressingFiles": "正在压缩日志文件...",
  "log.verifyingPackage": "正在验证压缩包...",
  "log.compressionComplete": "压缩完成，准备上传...",
  "log.uploadSuccess": "日志已成功上传，URL 已复制到剪贴板: {{url}}",
  "log.uploadFailed": "生成日志 URL 失败: {{message}}",
  "log.compressionHint": "\n提示：可能是日志文件过大或磁盘空间不足",
  "log.uploadHint": "\n提示：请检查网络连接和代理设置",
  "log.timeoutHint": "\n提示：操作超时，请稍后重试",
  "log.compressionFailed": "压缩包创建失败",
  "log.uploadReturnNoUrl": "上传成功但未返回 URL",
  "log.compressionTimeout": "压缩操作超时，请检查日志文件大小",
  "log.compressionTimeoutManual": "压缩操作超时（60秒），请检查日志文件大小",
  "log.readLogDirectoryFailed": "读取日志目录失败",
  "log.packageTooLarge": "压缩包过大 ({{sizeMB}}MB)，请联系管理员",
  "log.uploadTimeout": "上传超时（5分钟），请检查网络连接或文件大小",
  "log.uploadFailedWithFile": "上传失败: {{filename}}{{error}}",

  // 日志处理相关的日志消息
  "log.message.filteredFiles": "筛选出的日志文件",
  "log.message.readDirectoryFailed": "读取日志目录失败",
  "log.message.startCompression": "开始压缩日志文件",
  "log.message.compressionTimeout": "压缩超时",
  "log.message.compressionFailed": "压缩日志失败",
  "log.message.compressionComplete": "日志压缩完成",
  "log.message.packageCreationSuccess": "压缩包创建成功",
  "log.message.startUpload": "开始上传日志压缩包",
  "log.message.uploadStart": "开始上传",
  "log.message.uploadProgress": "上传进度",
  "log.message.uploadSuccess": "上传成功",
  "log.message.uploadFailed": "上传失败",
  "log.message.urlGenerationSuccess": "日志 URL 生成成功",
  "log.message.urlGenerationFailed": "生成日志 URL 失败",
  "log.message.packageDeleted": "已删除本地压缩包",
  "log.message.packageDeleteFailed": "删除本地压缩包失败",
};
