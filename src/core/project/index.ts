import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import * as fs from "fs";
import * as path from "path";
import * as child_process from "child_process";
import { GeneratePromptFile } from "../../shared/types";
import { getLanguage } from "../../utils/getLanugage";

export class Project extends CoreModule {
  constructor(ext: ContextManager) {
    super(ext);
  }

  /** 是不是代码仓库 */
  isGitRepo() {
    return !!this.getRepoPath();
  }

  /** 是不是快手代码仓库 */
  isKwaiGitRepo(remoteOriginUrl: string) {
    return remoteOriginUrl?.startsWith("*************************") || remoteOriginUrl?.startsWith("https://git.corp.kuaishou.com") || false;
  }

  /** 获取当前分支 */
  getCurrentBranch(repoPath: string) {
    try {
      const currentBranch = child_process.execSync(`git -C ${repoPath} rev-parse --abbrev-ref HEAD`)
        .toString().trim();
      return currentBranch;
    }
    catch (error) {
      return "";
    }
  }

  /** 获取当前分支最后一个提交记录 */
  getCurrentBranchLastCommit(repoPath: string) {
    try {
      const latestCommit = child_process.execSync(`git -C ${repoPath} log -1 --format=%H`)
        .toString().trim();
      return latestCommit;
    }
    catch (error) {
      return "";
    }
  }

  /** 获取当前分支最后一个提交记录 */
  getCurrentCommit() {
    return this.getCurrentBranchLastCommit(this.getRepoPath() || "");
  }

  /** 获取origin远程仓库地址 */
  getRemoteOriginUrl(path: string) {
    if (!path) {
      return "";
    }
    try {
      const remoteUrl = child_process.execSync(`git -C ${path} remote get-url origin`).toString().trim();
      return remoteUrl;
    }
    catch (error) {
      return "";
    }
  }

  /** 从workspace往上查找仓库路径 */
  getRepoPath(deep = 10) {
    const workspacePath = this.getWorkspacePath();
    if (!workspacePath) {
      return;
    }
    let currentDir = workspacePath;
    let turn = 0;
    while (true) {
      if (turn > deep) {
        return;
      }

      const gitPath = path.join(currentDir, ".git");
      const parentDir = path.dirname(currentDir);
      if (currentDir === parentDir) {
        return;
      }
      if (fs.existsSync(gitPath)) {
        if (fs.statSync(gitPath).isDirectory()) {
          return path.dirname(gitPath);
        }
      }
      else {
        currentDir = parentDir;
      }
      turn++;
    }
  }

  /** 获取工作区被Git标准忽略的目录列表 */
  getWorkspaceExcludeStandardDirList(repoPath: string, currentGitInfo: {
    currentBranch?: string;
    currentPath?: string;
  } = {}) {
    const manageByGitDir = this.getManageByGitDir(repoPath, currentGitInfo);
    const notManageByGitFile = this.getNotManageByGitExcludeStandardFile(repoPath, currentGitInfo.currentPath);
    const notManageByGitDir = notManageByGitFile.map(i => path.dirname(i));
    const filterArray = Array.from(new Set([...manageByGitDir, ...notManageByGitDir]));
    return filterArray.sort();
  }

  /** 获取被Git管理的目录列表 */
  getManageByGitDir(repoPath: string, currentGitInfo: {
    currentBranch?: string;
    currentPath?: string;
  } = {}) {
    const currentBranch = currentGitInfo?.currentBranch || "";
    const currentPath = currentGitInfo?.currentPath || "";
    const hasInfo = currentBranch || currentPath;

    let extraInfo = "";

    if (hasInfo) {
      extraInfo += `${currentBranch || "HEAD"}`;
      extraInfo += `${currentPath ? `:${currentPath}` : ""}`;
    }

    try {
      const manageByGitDir = child_process.execSync(`git -C ${repoPath} ls-tree -d -r --name-only ${extraInfo}`).toString().trim().split("\n");
      return manageByGitDir;
    }
    catch (error) {
      return [];
    }
  }

  /** 获取未被Git管理的标准忽略文件列表 */
  getNotManageByGitExcludeStandardFile(repoPath: string, currentPath?: string) {
    try {
      const notManageByGitFile = child_process.execSync(`git -C ${repoPath} ls-files --others --exclude-standard ${currentPath || ""}`).toString().trim().split("\n");
      return notManageByGitFile;
    }
    catch (error) {
      return [];
    }
  }

  /** 获取工作区path */
  getWorkspacePath() {
    const workspacePath = this.getWorkspaceUri()?.fsPath;
    return workspacePath;
  }

  getWorkspaceUri() {
    const workspaceUri = vscode.workspace.workspaceFolders?.[0]?.uri;
    return workspaceUri;
  }

  async readFileContent(relativePaths: string[]): Promise<GeneratePromptFile[]> {
    const workspaceUri = this.getWorkspaceUri();
    if (!workspaceUri) {
      throw new Error("No workspace folder found");
    }
    const results: GeneratePromptFile[] = [];

    for (const relativePath of relativePaths) {
      const fileUri = vscode.Uri.joinPath(workspaceUri, relativePath);

      try {
        const fileData = await vscode.workspace.fs.readFile(fileUri);
        const code = Buffer.from(fileData).toString("utf8");
        const language = getLanguage(fileUri);

        results.push({
          code,
          language,
          name: relativePath,
        });
      }
      catch (error) {
        results.push({
          code: "",
          language: "",
          name: relativePath,
        });
      }
    }

    return results;
  }

  getOpenTabFiles() {
    const rawData = vscode.window.tabGroups.all;
    const tabs: string[] = [];
    rawData.forEach((grow) => {
      const innerTab = grow?.tabs || [];

      const _tabs = innerTab.filter((tab) => {
        if (typeof tab === "string") {
          return false;
        }
        const input = tab?.input as any;
        if (input?.uri?.scheme !== "file") {
          return false;
        }

        return true;
      });
      const formatTab: string[] = _tabs.map((tab) => {
        const input = tab?.input as any;
        const fsPath = input?.uri?.fsPath;
        const k = vscode.workspace.asRelativePath(fsPath);
        return k;
      });
      tabs.push(...formatTab);
    });

    return Array.from(new Set(tabs));
  }

  getOpenTabsCount() {
    return this.getOpenTabFiles().length;
  }

  getOpenTabDir(fi?: string[]) {
    const files = fi || this.getOpenTabFiles();
    const dir = files.map((i) => {
      return path.dirname(vscode.workspace.asRelativePath(i));
    });
    return Array.from(new Set(dir));
  }
}
