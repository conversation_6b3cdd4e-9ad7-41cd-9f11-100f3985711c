import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import * as vscode from "vscode";
import { getNonce } from "../../utils/getNonce";

import { StateManager } from "../../common/state";
import { LoggerManager } from "../logger";
import { Bridge } from "@bridge";
import { ConfigManager, WorkspaceStateManager } from "../state-manager";
import { Config, WorkspaceState } from "../state-manager/types";
import { ReportKeys, ReportOpt } from "shared/lib/misc/logger";
import { getThemeInitConfig } from "../../services/theme";
import { WebloggerManager } from "../weblogger";
import { createExtensionRpcContext } from "../bridge/ExtensionRpcContext";
import {
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
} from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { KwaipilotEnv } from "../../const";
import { ExtensionWebviewShap } from "shared/lib/bridge/protocol";
import { ViewModel } from "shared";
import { Sleep } from "../../utils";
// FIXME: 第一次会从代码层面调用打开webview页面，所以忽略第一次上报
let flag = false;

export class Webview
  extends BaseModule
  implements vscode.WebviewViewProvider, ExtensionWebviewShap {
  _view?: vscode.WebviewView | vscode.WebviewPanel;
  private siderbarView: vscode.WebviewView | undefined;
  private panel: vscode.WebviewPanel | undefined;
  public provider?: vscode.Disposable;
  private currentViewModel: ViewModel = "siderbar";
  private routePath: string = "";

  // 用户关闭标签页，需要新建会话，而不是保留会话，但是通过webview提供的恢复可以保留
  private keepSession = false;

  rpcContext: IRPCProtocol;

  constructor(ext: ContextManager) {
    super(ext);
    this.registerWebview();
    StateManager.initInstance(this.context);
    setTimeout(() => {
      vscode.commands
        .executeCommand("workbench.view.extension.kwaipilot_sidebar")
        .then(() => {
          // 在cloudev内，自动打开kwaipilot侧边栏
          if (process.env.CLOUDDEV_CONTAINER === "1") {
            flag = true;
            return;
          }
          // 切换回文件树视图
          vscode.commands.executeCommand(
            "workbench.files.action.focusFilesExplorer",
          );
        });
    }, 100);
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(
            NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE,
            (data, source) => {
              if (source !== this._view?.webview) {
                return;
              }
              listener(data);
            },
          );
        },
        send: (message) => {
          const webview = this._view!.webview;
          this.getBase(Bridge).postOneWayMessage(
            webview,
            WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE,
            message,
          );
        },
      },
    });
    // 每次启动默认重置为siderbar
    this.setViewModel("siderbar");
    this.setFullModeNewWindow(false);
  }

  $getRestoreRoute(): string {
    return this.routePath;
  }

  $setRestoreRoute(path: string): void {
    this.routePath = path;
  }

  get isFullMode() {
    return this.currentViewModel === "panel";
  }

  private setFullModeNewWindow(value: boolean) {
    this.getBase(WorkspaceStateManager).update(
      WorkspaceState.FULL_MODE_NEW_WINDOW,
      value,
    );
  }

  private setViewModel(viewModel: ViewModel) {
    this.currentViewModel = viewModel;
    this.getBase(WorkspaceStateManager).update(
      WorkspaceState.VIEW_MODEL,
      viewModel,
    );
  }

  /**
   * 判断 webivew 是否初始化完成
   * @returns boolean
   */
  public isReady() {
    return !!this._view;
  }

  $reveal() {
    this.focus("ext_click");
  }

  /**
   * 切换全屏模式或侧边栏模式
   */
  $moveFullMode(newWindow: boolean): void {
    const param: ReportOpt<"full_model_switch"> = {
      key: "full_model_switch",
      type: newWindow ? "new_window" : "edit_window",
    };
    this.getBase(WebloggerManager)?.$reportUserAction(param);
    this.openFullModePanel(newWindow);
  }

  $moveToSidebar(): void {
    this.keepSession = true;
    if (this.panel) {
      this.closePanelAndReturnToSidebar();
    }
  }

  /**
   * 打开独立窗口模式
   */
  openFullModePanel(newWindow = false) {
    this.provider?.dispose();

    // 如果Panel已存在，直接显示
    if (this.panel) {
      this.panel.reveal();
      return;
    }

    try {
      // 创建新的Panel
      this.panel = vscode.window.createWebviewPanel(
        "kwaipilot-panel",
        "Kwaipilot",

        vscode.ViewColumn.Active,

        {
          enableScripts: true,
          retainContextWhenHidden: true,
        },
      );

      // 切换到Panel模式
      this.setViewModel("panel");
      this._view = this.panel;

      // 设置Panel图标
      this.setPanelIcon();

      // 设置Panel事件监听
      this.setupPanelEventListeners();

      // 设置WebView内容和消息处理
      this.setupWebviewContent(this.panel.webview);
      this.setupWebviewMessageHandling(this.panel.webview);
      if (newWindow) {
        this.openNewWindow();
      }
      this.logger.info("Panel创建成功", "webview", {
        value: { panelId: "kwaipilot-panel" },
      });
    }
    catch (error) {
      this.logger.error("创建Panel失败", "webview", { err: error });
    }
  }

  /**
   * 新窗口打开
   */
  private async openNewWindow() {
    this.setFullModeNewWindow(true);
    // 如果是新窗口模式，直接打开新窗口
    vscode.commands.executeCommand("workbench.action.moveEditorToNewWindow");
    // note: 这里的Sleep是为了确保新窗口打开后，能够正确应用紧凑模式
    await Sleep(400);
    await vscode.commands.executeCommand(
      "workbench.action.enableCompactAuxiliaryWindow",
    );
    await vscode.commands.executeCommand("workbench.action.lockEditorGroup");
  }

  /**
   * 关闭Panel并返回到侧边栏模式
   */
  private closePanelAndReturnToSidebar() {
    if (this.panel) {
      this.panel.dispose();
    }
  }

  /**
   * 设置Panel图标
   */
  private setPanelIcon() {
    if (!this.panel) return;

    const iconPath = {
      light: vscode.Uri.joinPath(
        this.context.extensionUri,
        "resources",
        "light",
        "kwaipilot.svg",
      ),
      dark: vscode.Uri.joinPath(
        this.context.extensionUri,
        "resources",
        "dark",
        "kwaipilot.svg",
      ),
    };
    this.panel.iconPath = iconPath;
  }

  /**
   * 设置Panel事件监听器
   */
  private setupPanelEventListeners() {
    if (!this.panel) return;

    // 监听Panel关闭事件
    this.panel.onDidDispose(
      () => {
        this.panel = undefined;
        this.setViewModel("siderbar");
        this._view = this.siderbarView;

        if (!this.keepSession) {
          this.getBase(WorkspaceStateManager).update(
            WorkspaceState.ACTIVE_COMPOSER_SESSION_ID,
            "",
          );
          this.getBase(WorkspaceStateManager).update(
            WorkspaceState.ACTIVE_SESSION_ID,
            "",
          );
        }
        else {
          // 保证每次点击恢复按钮只执行一次
          this.keepSession = false;
        }

        // 刷新侧边栏webview
        if (this.siderbarView) {
          vscode.commands.executeCommand(
            "workbench.action.webview.reloadWebviewAction",
          );
        }

        this.logger.info("Panel已关闭，切换回侧边栏模式", "webview");
      },
      null,
      this.context.subscriptions,
    );

    // 监听Panel视图状态变化事件（用于处理copy to new window等场景的焦点问题）
    this.panel.onDidChangeViewState(
      (e) => {
        // 当panel变为活动状态时，确保正确聚焦
        if (e.webviewPanel.active && e.webviewPanel.visible) {
          // 使用setTimeout确保在VS Code完成窗口切换后再聚焦
          setTimeout(() => {
            if (this.panel && this.panel.active) {
              this.panel.reveal(this.panel.viewColumn, false);
              this.logger.info("Panel已聚焦到新窗口", "webview", {
                value: {
                  active: e.webviewPanel.active,
                  visible: e.webviewPanel.visible,
                  viewColumn: e.webviewPanel.viewColumn,
                },
              });
            }
          }, 100);
        }
      },
      null,
      this.context.subscriptions,
    );
  }

  /**
   * 设置WebView内容
   */
  private setupWebviewContent(webview: vscode.Webview) {
    webview.html = this._getHtmlForWebview(webview, this.context.extensionUri);
  }

  private setupWebviewMessageHandling(webview: vscode.Webview) {
    webview.onDidReceiveMessage((message) => {
      if (message.protocol === "callHandler") {
        this.getBase(Bridge).callNativeHandler(
          webview,
          message.name,
          message.data,
          message.callbackId,
        );
      }
      else if (message.protocol === "callback") {
        this.getBase(Bridge).handleCallback(message.callbackId, message.data);
      }
      else if (message.protocol === "message") {
        this.getBase(Bridge).handleOneWayMessage(webview, message.data);
      }
    });
  }

  public resolveWebviewView(view: vscode.WebviewView) {
    this._view = view;
    this.siderbarView = view;

    view.onDidChangeVisibility(() => {
      if (view.visible && flag) {
        const param: ReportOpt<"sidebar_show"> = {
          key: "sidebar_show",
          type: "ext_click",
        };
        flag = true;
        this.getBase(WebloggerManager)?.$reportUserAction(param);
      }
    });

    view.webview.options = {
      enableScripts: true,
      localResourceRoots: [this.context.extensionUri],
    };

    // 使用提取的公共方法
    this.setupWebviewContent(view.webview);
    this.setupWebviewMessageHandling(view.webview);
  }

  public revive(panel: vscode.WebviewView) {
    this._view = panel;
  }

  /**
   * 显示并聚焦 webivew 页面
   * @returns Promise
   */
  public focus(form: ReportKeys["sidebar_show"] | ReportKeys["panel_show"]) {
    if (this.currentViewModel === "siderbar") {
      const param: ReportOpt<"sidebar_show"> = {
        key: "sidebar_show",
        type: form,
      };
      this.getBase(WebloggerManager)?.$reportUserAction(param);
      return vscode.commands.executeCommand("kwaiPilotChatWebView.focus");
    }
    else if (this.currentViewModel === "panel") {
      const param: ReportOpt<"panel_show"> = {
        key: "panel_show",
        type: form,
      };
      this.getBase(WebloggerManager)?.$reportUserAction(param);
      return this.panel?.reveal();
    }
  }

  public postMessage(message: any) {
    // this.send(`_OLD_${message.type}`, message);
    this._view?.webview.postMessage(message);
  }

  private registerWebview() {
    this.provider = vscode.window.registerWebviewViewProvider(
      "kwaiPilotChatWebView",
      this,
      { webviewOptions: { retainContextWhenHidden: true } },
    );
    this.context.subscriptions.push(this.provider);

    // 注册打开侧边栏命令
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.Open Kwaipilot Panel", () => {
        vscode.commands.executeCommand(
          "workbench.view.extension.kwaipilot_sidebar",
        );
      }),
    );
  }

  /**
   * 获取webView视图
   * @param webview webView组件
   * @returns
   */
  private _getHtmlForWebview(
    webview: vscode.Webview,
    extensionUri: vscode.Uri,
  ) {
    const inDevelopmentMode
      = this.context.extensionMode === vscode.ExtensionMode.Development;
    const vscMediaUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "webview-ui/assets"))
      .toString();

    const jsUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "bridge/index.js"))
      .toString();

    let scriptUri: string;
    let styleMainUri: string;
    if (!inDevelopmentMode) {
      scriptUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "webview-ui/build/assets/index.js"),
        )
        .toString();
      styleMainUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(
            extensionUri,
            "webview-ui/build/assets/index.css",
          ),
        )
        .toString();
    }
    else {
      scriptUri = "http://localhost:5173/src/index.tsx";
      styleMainUri = "http://localhost:5173/src/App.css";
    }
    this.logger.info("init webview", "webview", {
      value: {
        scriptUri,
        styleMainUri,
        vscMediaUrl,
      },
    });
    const nonce = getNonce();
    const proxyUrl = this.getBase(ConfigManager).get(Config.PROXY_URL);
    const injectTheme = getThemeInitConfig() ?? {};
    const composerSessionId
      = this.getBase(WorkspaceStateManager).get(
        WorkspaceState.ACTIVE_COMPOSER_SESSION_ID,
      ) ?? "";
    const editorLanguage = KwaipilotEnv.isInIde ? vscode.env.language : "zh-cn"; // 插件里始终用中文
    const viewModel = this.currentViewModel;

    return `<!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleMainUri}" rel="stylesheet">
        <script>window.vscMediaUrl = "${vscMediaUrl}"</script>
        <script>window.proxyUrl = "${proxyUrl}"</script>
        <script>window.__KWAIPILOT_VIEW_TYPE__ = "${viewModel}"</script>
        <script>window.composerSessionId = "${composerSessionId}"</script>
        <script>window.ide = "vscode"</script>
        <script>window.editorLanguage = "${editorLanguage}"</script>
        <script>window.__injectTheme__ = ${JSON.stringify(injectTheme)}</script>
        <script>window.colorThemeName = "dark"</script>
        <script nonce="${nonce}" src="${jsUrl}"></script>

        <title>Continue</title>
      </head>
      <body data-element-type="body">
        <div id="root" data-element-type="root"></div>
        ${
          inDevelopmentMode
            ? `<script type="module">
          import RefreshRuntime from "http://localhost:5173/@react-refresh"
          RefreshRuntime.injectIntoGlobalHook(window)
          window.$RefreshReg$ = () => {}
          window.$RefreshSig$ = () => (type) => type
          window.__vite_plugin_react_preamble_installed__ = true
          </script>
          <script type="module">
            import { createHotContext } from "http://localhost:5173/@vite/client"
            window.__vite_hot_context__ = createHotContext()
          </script>`
            : ""
        }
        <script type="module" nonce="${nonce}" src="${scriptUri}"></script>
      </body>
    </html>`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
