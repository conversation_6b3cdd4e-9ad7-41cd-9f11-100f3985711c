{"kwaipilot.showInlineCompletion.title": "Kwaipilot: Trigger Inline Completion", "kwaipilot.newComposer.title": "Open Code Agent", "kwaipilot.inlineChat.title": "Kwaipilot: Conversational Code Generation", "kwaipilot.addToComposerContext.title": "Add Selected Code Lines to \"Knowledge\"", "kwaipilot.addFileToContext.title": "Add to \"Knowledge\"", "kwaipilot.generateCommentMessage.title": "Kwaipilot: Generate Code Comments", "kwaipilot.generateUnitTest.title": "Kwaipilot: Generate Unit Tests", "kwaipilot.explain.title": "Kwaipilot: Explain Code", "kwaipilot.home.title": "Go to Q&A Engine", "kwaipilot.Open Kwaipilot Panel.title": "Kwaipilot: Intelligent Programming Assistant", "kwaipilot.feedback.title": "User <PERSON>", "kwaipilot.help.title": "Help Documentation", "kwaipilot.settings.title": "Settings", "kwaipilot.login.title": "<PERSON><PERSON>", "kwaipilot.logout.title": "Logout", "kwaipilot.reloadWebview.title": "Refresh Webview", "kwaipilot.quickAsk.title": "Kwaipilot: Quick Q&A", "kwaipilot.showDiff.title": "Kwaipilot: Show Code Block", "kwaipilot.fileDiff.title": "Kwaipilot: Show File Diff in Single File", "kwaipilot.addDataAnnotation.title": "Kwaipilot: Data Enhancement Annotation", "kwaipilot.handleEscape.title": "Kwaipilot: <PERSON><PERSON> Esc Key", "kwaipilot.handleTab.title": "Kwaipilot: <PERSON><PERSON>", "kwaipilot.openCodeIndexManagement.title": "Kwaipilot: Code Index Management", "kwaipilot.openRulesManagement.title": "Kwaipilot: Rules Management", "kwaipilot.openBasicsManagement.title": "Kwaipilot: Basic Configuration", "kwaipilot.openFunctionManagement.title": "Kwaipilot: Function Configuration", "kwaiPilot.reportBadCase.title": "Kwaipilot: Report Bad Case", "kwaipilot.acceptDiff.title": "Accept Changes", "kwaipilot.rejectDiff.title": "Reject Changes", "kwaipilot.nextDiffFile.title": "Next Diff File", "kwaipilot.previousDiffFile.title": "Previous Diff File", "kwaipilot.lineAccept.title": "Accept Line Continuation"}