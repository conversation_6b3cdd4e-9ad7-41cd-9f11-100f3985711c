export const zh = {
  // 主导航标签
  "nav.basics": "基础",
  "nav.function": "功能",
  "nav.fileIndex": "代码索引",
  "nav.mcp": "MCP",
  "nav.rules": "规则配置",

  // 基础设置页面
  "basics.general.title": "通用",
  "basics.account.title": "账户",
  "basics.account.settings": "账号设置",
  "basics.account.currentUser": "当前登录账号：",
  "basics.account.notLoggedIn": "未登录",
  "basics.account.logout": "注销",
  "basics.account.login": "登录",
  "basics.configMigration.title": "配置迁移",
  "basics.configMigration.description": "导入 VS Code 或 Cursor 中的所有插件、设置、MCP以及快捷键配置到 Kwaipilot 中，请注意，导入后将覆盖当前配置，且不可恢复。",
  "basics.ideSetting.title": "IDE设置",
  "basics.ideSetting.description": "前往文本、工作台、窗口等IDE基础设置",
  "basics.ideSetting.button": "去设置",
  "basics.shortcutSetting.title": "快捷键设置",
  "basics.shortcutSetting.description": "前往IDE的快捷键自定义设置",
  "basics.shortcutSetting.button": "去设置",
  "basics.other.title": "其他",
  "basics.proxyUrl.title": "代理URL",
  "basics.proxyUrl.description": "内部调试使用",
  "basics.proxyUrl.prod": "生产环境(默认)",
  "basics.proxyUrl.pre": "预发环境",
  "basics.proxyUrl.test": "测试环境",
  "basics.proxyUrl.idc": "IDC环境",
  "basics.modelType.title": "推理模型",
  "basics.modelType.description": "内部调试使用",
  "basics.about.title": "关于",
  "basics.userAgreement": "用户协议",
  "basics.privacy": "隐私政策",
  "basics.openSource": "开源软件声明",

  // 功能设置页面
  "function.codeCompletion.title": "代码续写",
  "function.codeCompletion.enable.title": "代码续写",
  "function.codeCompletion.enable.description": "基于当前编辑行为智能生成多行代码编辑推荐",
  "function.codeCompletion.commentEnable.title": "注释默认续写",
  "function.codeCompletion.commentEnable.description": "开启后注释内容也会出现续写提示",
  "function.codeCompletion.delay.title": "续写等待时间",
  "function.codeCompletion.delay.description": "在等待时间(ms)结束后才会出现代码续写提示",
  "function.codeCompletion.prediction.title": "启用代码编辑预测",
  "function.codeCompletion.prediction.description": "基于当前编辑行为智能预测下一个改动点并生成多行代码编辑推荐",
  "function.codeBlock.title": "代码块操作",
  "function.codeBlock.enable.title": "代码块操作",
  "function.codeBlock.enable.description": "启用代码块操作、函数注释、行间注释等功能展示",
  "function.inlineTip.title": "智能体模式",
  "function.agentPreference.title": "智能体模式偏好",
  "function.agentPreference.intelligent": "智能优先",
  "function.agentPreference.speed": "速度优先",
  "function.agentPreference.intelligentDesc": "更强的推理和上下文理解能力，支持多模态输入，适合复杂或多步编辑的开发任务",
  "function.agentPreference.speedDesc": "更快的响应速度，仅支持文本输入，适合快速验证或频繁迭代的开发任务",
  "function.diagnosticsCheck.title": "自动检测lint问题",
  "function.diagnosticsCheck.description": "开启后，在智能体模式下将检测生成代码的lint错误和一键修复建议",

  // 自动运行模式
  "autoRun.title": "自动运行模式",
  "autoRun.description": "开启后智能体将无需确认自动运行合理的工具，例如命令行执行和MCP执行等",
  "autoRun.confirmTitle": "切换为自动运行",
  "autoRun.confirmMessage": "开启后，智能体将自动执行除黑名单外的命令，请注意可能的潜在安全风险",
  "autoRun.confirmButton": "确认",
  "autoRun.cancelButton": "取消",
  "autoRun.blacklist.title": "命令黑名单",
  "autoRun.blacklist.description": "命令前缀在黑名单内的命令，将不会自动执行，始终会询问用户",
  "autoRun.blacklist.placeholder": "请输入名称",
  "autoRun.blacklist.addButton": "添加",
  "autoRun.mcp.title": "mcp自动运行",
  "autoRun.mcp.description": "启用后，将允许智能体自动运行MCP",

  // 代码索引页面
  "fileIndex.title": "代码索引",
  "fileIndex.description": "构建仓库代码的全局索引，当发起智能体会话时将自动检索问题相关上下文，提升代码问答准确性",
  "fileIndex.autoEnable.title": "自动构建索引",
  "fileIndex.autoEnable.description": "开启后新开项目将自动开始构建",
  "fileIndex.ignoreFolder.title": "忽略/指定文件目录",
  "fileIndex.ignoreFolder.description": "配置构建代码索引时忽略或指定的文件目录",
  "fileIndex.ignoreFolder.button": "前往配置",
  "fileIndex.maxSpace.title": "最大索引空间大小(单位GB)",
  "fileIndex.startBuild": "开始构建",
  "fileIndex.cancelBuild": "取消构建",
  "fileIndex.rebuild": "重新构建",
  "fileIndex.deleteIndex": "删除索引",
  "fileIndex.building": "构建索引中...",
  "fileIndex.buildSuccess": "构建成功",
  "fileIndex.notRepo": "不是git仓库",
  "fileIndex.paused": "已暂停索引",
  "fileIndex.notBuilt": "当前未构建索引",

  // MCP 页面
  "mcp.title": "MCP 管理",
  "mcp.servers": "MCP Servers",
  "mcp.description": "模型上下文协议 (Model Context Protocol, MCP) 是一种为 Kwaipilot 助理提供工具和功能来扩展助理的能力",
  "mcp.descriptionWithMarket": "模型上下文协议 (Model Context Protocol, MCP) 是一种为 Kwaipilot 智能体模式提供工具和功能来扩展智能体能力。详细说明及环境准备请见",
  "mcp.market": "MCP市场",
  "mcp.manualConfig": "手动配置",
  "mcp.quickAdd": "快速添加",
  "mcp.noServers": "暂未添加MCP Servers，请添加",
  "mcp.tooManyTools": "已启用 {{0}} 个工具，超40个工具会降低性能且部分模型不支持，系统将自动裁剪多余工具。",
  "mcp.status.connected": "可使用",
  "mcp.status.connecting": "准备中",
  "mcp.status.disconnected": "不可使用",
  "mcp.tooltip.refresh": "更新",
  "mcp.tooltip.edit": "编辑",
  "mcp.tooltip.delete": "删除",
  "mcp.tools": "Tools:",
  "mcp.toolParams": "参数",
  "mcp.required": "*",

  // 规则配置页面
  "rules.title": "规则配置",
  "rules.personalRule.title": "个人规则",
  "rules.personalRule.description": "在此文件中配置用户习惯后，Kwaipilot在问答模式及智能体模式的所有对话场景中均遵循设定规则，且跨项目切换时持续生效。",
  "rules.personalRule.button": "打开",
  "rules.projectRule.title": "项目规则",
  "rules.projectRule.description": "配置项目内使用的规则，在当前项目的问答模式与智能体模式的会话中生效，可在当前工作区的.kwaipilot/rules文件夹查看当前项目内的所有规则。",
  "rules.projectRule.button": "打开",

  // 导入按钮组件
  "import.button": "导入{{0}} 配置",
  "import.loading": "导入中...",
  "import.confirmTitle": "确认导入 {{0}} 配置吗？",
  "import.confirmMessage": "导入后将覆盖 Kwaipilot 当前配置，且不可恢复",
  "import.confirm": "确认",
  "import.cancel": "取消",

  // 通用按钮和操作
  "common.confirm": "确认",
  "common.cancel": "取消",
  "common.add": "添加",
  "common.delete": "删除",
  "common.edit": "编辑",
  "common.save": "保存",
  "common.open": "打开",
  "common.close": "关闭",
  "common.refresh": "刷新",
  "common.loading": "加载中...",
  "common.success": "成功",
  "common.error": "错误",
  "common.warning": "警告",

  // MCP 快捷添加按钮
  "mcp.quickAdd.button": "快捷配置",
  "mcp.quickAdd.placeholder": "请输入MCP server名称",
  "mcp.quickAdd.noData": "暂无数据",
  "mcp.quickAdd.featured": "精选",
  "mcp.quickAdd.kuaishou": "快手",
  "mcp.quickAdd.added": "已添加",
  "mcp.quickAdd.add": "添加",
  "mcp.quickAdd.otherServers": "其他 MCP Server 可前往",
  "mcp.quickAdd.marketLink": "MCP市场",
  "mcp.quickAdd.manualAdd": "进行手动添加",

  // MCP 错误信息
  "mcp.error.fetchServersFailed": "获取MCP服务器列表失败",
  "mcp.featureIntroduction": "MCP 功能介绍",

  // MCP 快速添加对话框
  "mcp.quickAdd.title": "添加 MCP Server",
  "mcp.quickAdd.description": "您将添加 {{0}} ，此MCP Server无需提供额外配置信息，点击\"确认\"完成添加。",
  "mcp.quickAdd.installFailed": "安装失败",

  // 语言设置
  "basics.language.title": "语言",
  "basics.language.description": "配置Kwaipilot使用的文本语言",

  "basics.exportLog": "日志导出",
  "basics.copyLogUrl": "复制日志链接",
};
