export const en = {
  // Main navigation labels
  "nav.basics": "Basics",
  "nav.function": "Functions",
  "nav.fileIndex": "File Index",
  "nav.mcp": "MCP",
  "nav.rules": "Rules",

  // Basic settings page
  "basics.general.title": "General",
  "basics.account.title": "Account",
  "basics.account.settings": "Account Settings",
  "basics.account.currentUser": "Current logged in account:",
  "basics.account.notLoggedIn": "Not logged in",
  "basics.account.logout": "Logout",
  "basics.account.login": "Login",
  "basics.configMigration.title": "Configuration Migration",
  "basics.configMigration.description": "Import all plugins, settings, MCP and shortcut configurations from VS Code or Cursor to Kwaipilot. Note: Importing will overwrite current configurations and cannot be recovered.",
  "basics.ideSetting.title": "IDE Settings",
  "basics.ideSetting.description": "Go to IDE basic settings such as text, workbench, window etc.",
  "basics.ideSetting.button": "Go to Settings",
  "basics.shortcutSetting.title": "Shortcut Settings",
  "basics.shortcutSetting.description": "Go to IDE shortcut customization settings",
  "basics.shortcutSetting.button": "Go to Settings",
  "basics.other.title": "Other",
  "basics.proxyUrl.title": "Proxy URL",
  "basics.proxyUrl.description": "For internal debugging use",
  "basics.proxyUrl.prod": "Production (Default)",
  "basics.proxyUrl.pre": "Pre-release",
  "basics.proxyUrl.test": "Test Environment",
  "basics.proxyUrl.idc": "IDC Environment",
  "basics.modelType.title": "Inference Model",
  "basics.modelType.description": "For internal debugging use",
  "basics.about.title": "About",
  "basics.userAgreement": "User Agreement",
  "basics.privacy": "Privacy Policy",
  "basics.openSource": "Open Source Statement",

  // Language settings
  "basics.language.title": "Language",
  "basics.language.description": "Configure the text language used by Kwaipilot",

  // Function settings page
  "function.codeCompletion.title": "Code Completion",
  "function.codeCompletion.enable.title": "Code Completion",
  "function.codeCompletion.enable.description": "Intelligently generate multi-line code editing recommendations based on current editing behavior",
  "function.codeCompletion.commentEnable.title": "Comment Default Completion",
  "function.codeCompletion.commentEnable.description": "When enabled, comment content will also show completion suggestions",
  "function.codeCompletion.delay.title": "Completion Wait Time",
  "function.codeCompletion.delay.description": "Code completion suggestions will appear after the wait time (ms) ends",
  "function.codeCompletion.prediction.title": "Enable Code Edit Prediction",
  "function.codeCompletion.prediction.description": "Intelligently predict the next change point based on current editing behavior and generate multi-line code editing recommendations",
  "function.codeBlock.title": "Code Block Operations",
  "function.codeBlock.enable.title": "Code Block Operations",
  "function.codeBlock.enable.description": "Enable code block operations such as \"Function Comment\" and \"Line Comment\" features",
  "function.inlineTip.title": "Agent Mode",
  "function.agentPreference.title": "Agent Mode Preference",
  "function.agentPreference.intelligent": "Intelligence First",
  "function.agentPreference.speed": "Speed First",
  "function.agentPreference.intelligentDesc": "Stronger reasoning and contextual understanding capabilities, supports multimodal input, suitable for complex or multi-step development tasks",
  "function.agentPreference.speedDesc": "Faster response speed, text input only, suitable for rapid validation or frequent iteration development tasks",
  "function.diagnosticsCheck.title": "Auto Detect Lint Issues",
  "function.diagnosticsCheck.description": "When enabled, lint errors and one-click fix suggestions will be detected for generated code in agent mode",

  // Auto-run mode
  "autoRun.title": "Auto Run Mode",
  "autoRun.description": "When enabled, the agent will automatically run reasonable tools without confirmation, such as command line execution and MCP execution",
  "autoRun.confirmTitle": "Switch to Auto Run",
  "autoRun.confirmMessage": "When enabled, the agent will automatically execute commands except those in the blacklist. Please be aware of potential security risks",
  "autoRun.confirmButton": "Confirm",
  "autoRun.cancelButton": "Cancel",
  "autoRun.blacklist.title": "Command Blacklist",
  "autoRun.blacklist.description": "Commands with prefixes in the blacklist will not be executed automatically and will always ask the user",
  "autoRun.blacklist.placeholder": "Please enter name",
  "autoRun.blacklist.addButton": "Add",
  "autoRun.mcp.title": "MCP Auto Run",
  "autoRun.mcp.description": "When enabled, allows the agent to automatically run MCP",

  // Code index page
  "fileIndex.title": "Code Index",
  "fileIndex.description": "Build a global index of repository code. When initiating agent conversations, relevant context will be automatically retrieved to improve code Q&A accuracy",
  "fileIndex.autoEnable.title": "Auto Build Index",
  "fileIndex.autoEnable.description": "When enabled, newly opened projects will automatically start building",
  "fileIndex.ignoreFolder.title": "Ignore/Specify File Directories",
  "fileIndex.ignoreFolder.description": "Configure file directories to ignore or specify when building code index",
  "fileIndex.ignoreFolder.button": "Go to Configuration",
  "fileIndex.maxSpace.title": "Maximum Index Space Size (GB)",
  "fileIndex.startBuild": "Start Build",
  "fileIndex.cancelBuild": "Cancel Build",
  "fileIndex.rebuild": "Rebuild",
  "fileIndex.deleteIndex": "Delete Index",
  "fileIndex.building": "Building index...",
  "fileIndex.buildSuccess": "Build successful",
  "fileIndex.notRepo": "Not a git repository",
  "fileIndex.paused": "Index paused",
  "fileIndex.notBuilt": "Index not built",

  // MCP page
  "mcp.title": "MCP Management",
  "mcp.servers": "MCP Servers",
  "mcp.description": "Model Context Protocol (MCP) is a protocol that provides tools and functionality to extend the capabilities of Kwaipilot assistant",
  "mcp.descriptionWithMarket": "Model Context Protocol (MCP) is a protocol that provides tools and functionality to extend the capabilities of Kwaipilot assistant. You can add MCP Servers manually or from Kuaishou MCP Market",
  "mcp.market": "MCP Market",
  "mcp.manualConfig": "Manual Configuration",
  "mcp.quickAdd": "Quick Add",
  "mcp.noServers": "No MCP Servers added yet, please add",
  "mcp.tooManyTools": "{{0}} tools enabled. More than 40 tools will reduce performance and some models don't support it. The system will automatically trim excess tools.",
  "mcp.status.connected": "Available",
  "mcp.status.connecting": "Preparing",
  "mcp.status.disconnected": "Unavailable",
  "mcp.tooltip.refresh": "Refresh",
  "mcp.tooltip.edit": "Edit",
  "mcp.tooltip.delete": "Delete",
  "mcp.tools": "Tools:",
  "mcp.toolParams": "Parameters",
  "mcp.required": "*",

  // Rules configuration page
  "rules.title": "Rules Configuration",
  "rules.personalRule.title": "Personal Rules",
  "rules.personalRule.description": "After configuring user habits in this file, Kwaipilot will follow the set rules in all conversation scenarios in Q&A mode and agent mode, and will continue to be effective when switching between projects.",
  "rules.personalRule.button": "Open",
  "rules.projectRule.title": "Project Rules",
  "rules.projectRule.description": "Configure rules used within the project, effective in Q&A mode and agent mode conversations of the current project. You can view all rules within the current project in the .kwaipilot/rules folder of the current workspace.",
  "rules.projectRule.button": "Open",

  // Import button component
  "import.button": "Import {{0}} Config",
  "import.loading": "Importing...",
  "import.confirmTitle": "Confirm import {{0}} configuration?",
  "import.confirmMessage": "Importing will overwrite current Kwaipilot configuration and cannot be recovered",
  "import.confirm": "Confirm",
  "import.cancel": "Cancel",

  // Common buttons and actions
  "common.confirm": "Confirm",
  "common.cancel": "Cancel",
  "common.add": "Add",
  "common.delete": "Delete",
  "common.edit": "Edit",
  "common.save": "Save",
  "common.open": "Open",
  "common.close": "Close",
  "common.refresh": "Refresh",
  "common.loading": "Loading...",
  "common.success": "Success",
  "common.error": "Error",
  "common.warning": "Warning",

  // MCP Quick Add Button
  "mcp.quickAdd.button": "Quick Configuration",
  "mcp.quickAdd.placeholder": "Enter MCP server name",
  "mcp.quickAdd.noData": "No data available",
  "mcp.quickAdd.featured": "Featured",
  "mcp.quickAdd.kuaishou": "Kuaishou",
  "mcp.quickAdd.added": "Added",
  "mcp.quickAdd.add": "Add",
  "mcp.quickAdd.otherServers": "Other MCP Servers can go to",
  "mcp.quickAdd.marketLink": "MCP Market",
  "mcp.quickAdd.manualAdd": "for manual addition",

  // MCP Error Messages
  "mcp.error.fetchServersFailed": "Failed to fetch MCP server list",
  "mcp.featureIntroduction": "MCP Feature Introduction",

  // MCP Quick Add Dialog
  "mcp.quickAdd.title": "Add MCP Server",
  "mcp.quickAdd.description": "You are about to add {{0}}. This MCP Server requires no additional configuration. Click \"Confirm\" to complete the addition.",
  "mcp.quickAdd.installFailed": "Installation failed",

  "basics.exportLog": "Export Log",
  "basics.copyLogUrl": "copy log url",
};
