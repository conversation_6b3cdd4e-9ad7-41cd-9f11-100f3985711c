import { Config } from "shared/lib/state-manager/types";
import { kwaiPilotBridgeAPI } from "../bridge";
import { userSettingsStore } from "../store/useSettingsStore";
import { MaxFileIndexSpace } from "../components/FileIndexMaxSpace";
import { FileIndexManualBuild } from "../components/FileIndexManualBuild";
import ImportButton from "../components/ImportButton";
import { AutoRun } from "@/components/AutoRun";
import { t } from "i18next";

export type SettingHost = "ide" | "plugin";
export interface SchemaItemBase<T> {
  title?: string;
  type: T;
  description?: string | ((config?: ReturnType<typeof userSettingsStore>) => string);
  default?: any;
  markdownDescription?: string | Array<string>;
  level?: number;
  host?: Array<SettingHost>; // 默认['plugin', 'ide']
  when?: string;
  renderRight?: (value?: ReturnType<typeof userSettingsStore>) => JSX.Element | null; // 自定义渲染右侧部分
  render?: (value?: ReturnType<typeof userSettingsStore>) => JSX.Element | null; // 自定义渲染整个模块
}

export interface AccountSchemaItem extends SchemaItemBase<"account"> {
  type: "account";
}

export interface ActionSchemaItem extends SchemaItemBase<"action"> {
  type: "action";
  actions: Array<{ title: string; action?: () => void } | { render: () => JSX.Element }>;
}

export interface StringSchemaItem extends SchemaItemBase<"string"> {
  type: "string";
}
export interface NumberSchemaItem extends SchemaItemBase<"integer"> {
  type: "integer";
  min?: number;
  max?: number;
}

export interface BooleanSchemaItem extends SchemaItemBase<"boolean"> {
  type: "boolean";
}

export interface EnumSchemaItem extends SchemaItemBase<"enum"> {
  type: "enum";
  enum: Array<string | number>;
  enumItemLabels?: string[];
}
export interface LinkSchemaItem extends SchemaItemBase<"link"> {
  type: "link";
  url: string;
}

export type SchemaItem = AccountSchemaItem | ActionSchemaItem | StringSchemaItem | NumberSchemaItem | BooleanSchemaItem | EnumSchemaItem | LinkSchemaItem;

export type PropertyKey = string;

export interface GroupSchemaItem {
  title: string;
  host?: Array<SettingHost>; // 默认plugin
  when?: string;
  properties: Record<PropertyKey, SchemaItem>;
}
// 使用getter模式，确保每次访问时都重新执行翻译函数
const CommonSettings: Record<string, Record<string, GroupSchemaItem>> = {
  get basic() {
    return {
      general: {
        title: t("basics.general.title"),
        properties: {
          account: {
            type: "account" as const,
          },
          configMigration: {
            title: t("basics.configMigration.title"),
            type: "action" as const,
            description: t("basics.configMigration.description"),
            actions: [
              {
                render: () => <ImportButton key="vscode" productName="VSCode" onConfirm={() => kwaiPilotBridgeAPI.extensionSettings.$importSettings("VSCode")} />,
              }, {
                render: () => <ImportButton key="cursor" productName="Cursor" onConfirm={() => kwaiPilotBridgeAPI.extensionSettings.$importSettings("Cursor")} />,
              },
            ],
            host: ["ide" as const],
          },
          ideSetting: {
            title: t("basics.ideSetting.title"),
            description: t("basics.ideSetting.description"),
            type: "action" as const,
            actions: [{
              title: t("basics.ideSetting.button"),
              action: () => kwaiPilotBridgeAPI.extensionSettings.$openIdeUserSettings(),
            }],
            host: ["ide" as const],
          },
          shortcutSetting: {
            title: t("basics.shortcutSetting.title"),
            description: t("basics.shortcutSetting.description"),
            type: "action" as const,
            actions: [{
              title: t("basics.shortcutSetting.button"),
              action: () => kwaiPilotBridgeAPI.extensionSettings.$openIdeShortcutSettings(),
            }],
            host: ["ide" as const],
          },
        },
      },
      other: {
        title: t("basics.other.title"),
        properties: {
          [Config.PROXY_URL]: {
            type: "enum" as const,
            host: ["plugin" as const],
            enum: [
              "https://kwaipilot.corp.kuaishou.com",
              "https://pre-kinsight.test.gifshow.com",
              "https://qa-kinsight.staging.kuaishou.com",
              "https://kinsight.corp.kuaishou.com",
            ],
            enumItemLabels: [
              t("basics.proxyUrl.prod"),
              t("basics.proxyUrl.pre"), // (pre-kinsight)
              t("basics.proxyUrl.test"), // (qa-kinsight)
              t("basics.proxyUrl.idc"), // (kinsight)
            ],
            title: t("basics.proxyUrl.title"),
            description: t("basics.proxyUrl.description"),
            default: "https://kwaipilot.corp.kuaishou.com",
          },
          [Config.MODEL_TYPE]: {
            type: "string" as const,
            host: ["plugin" as const],
            title: t("basics.modelType.title"),
            default: "",
            description: t("basics.modelType.description"),
          },
          [Config.KWAIPILOT_LANG]: {
            type: "enum" as const,
            host: ["ide" as const],
            enum: [
              "en",
              "zh-cn",
            ],
            enumItemLabels: [
              "English",
              "简体中文",
            ],
            title: t("basics.language.title"),
            description: t("basics.language.description"),
            default: window.editorLanguage,
          },
          copyLogUrl: {
            type: "action" as const,
            title: t("basics.exportLog"),
            default: "",
            description: "",
            actions: [{
              title: t("basics.copyLogUrl"),
              action: () => {
                kwaiPilotBridgeAPI.extensionSettings.$generateLogUrl();
              },
            }],
          },
        },
      },
      about: {
        title: t("basics.about.title"),
        host: ["ide" as const],
        properties: {
          user: {
            type: "link" as const,
            title: t("basics.userAgreement"),
            url: "",
          },
          privacy: {
            type: "link" as const,
            title: t("basics.privacy"),
            url: "",
          },
          openSource: {
            type: "link" as const,
            title: t("basics.openSource"),
            url: "",
          },
        },
      },
    };
  },
  get functions() {
    return {
      codeCompletion: {
        title: t("function.codeCompletion.title"),
        properties: {
          [Config.ENABLE]: {
            type: "boolean" as const,
            default: true,
            title: t("function.codeCompletion.enable.title"),
            description: t("function.codeCompletion.enable.description"),
          },
          [Config.COMMENT_COMPLETION_ENABLE]: {
            title: t("function.codeCompletion.commentEnable.title"),
            type: "boolean" as const,
            description: t("function.codeCompletion.commentEnable.description"),
            default: false,
            level: 2,
            when: "!!config.enable",
          },
          [Config.CODE_COMPLETION_DELAY]: {
            type: "integer" as const,
            title: t("function.codeCompletion.delay.title"),
            description: t("function.codeCompletion.delay.description"),
            default: 75,
            level: 2,
            host: ["plugin" as const],
            when: "!!config.enable",
          },
          [Config.PREDICTION_ENABLE]: {
            title: t("function.codeCompletion.prediction.title"),
            host: ["plugin" as const],
            description: t("function.codeCompletion.prediction.description"),
            type: "boolean" as const,
            default: true,
          },
        },
      },
      codeBlock: {
        title: t("function.codeBlock.title"),
        host: ["plugin" as const],
        properties: {
          [Config.ENABLE_CODE_BLOCK_ACTION]: {
            type: "boolean" as const,
            default: false,
            title: t("function.codeBlock.enable.title"),
            description: t("function.codeBlock.enable.description"),
          },
        },
      },
      inlineTip: {
        title: t("function.inlineTip.title"),
        properties: {
          [Config.AGENT_PREFERENCE]: {
            type: "enum" as const,
            enum: [
              "intelligent",
              "speed",
            ],
            description(config?: ReturnType<typeof userSettingsStore>) {
              if (!config) {
                return "";
              }
              if ((config as any)[Config.AGENT_PREFERENCE] === "intelligent") {
                return t("function.agentPreference.intelligentDesc");
              }
              else {
                return t("function.agentPreference.speedDesc");
              }
            },
            enumItemLabels: [
              t("function.agentPreference.intelligent"),
              t("function.agentPreference.speed"),
            ],
            title: t("function.agentPreference.title"),
            default: "intelligent",
          },
          [Config.ENABLE_DIAGNOSTICS_CHECK]: {
            type: "boolean" as const,
            title: t("function.diagnosticsCheck.title"),
            description: t("function.diagnosticsCheck.description"),
            default: true,
          },
          autoRun: {
            render() {
              return (
                <AutoRun />
              );
            },
            type: "action" as const,
            title: "",
            actions: [],
          },
        },
      },
    };
  },

  get fileIndex() {
    return {
      fileIndex: {
        title: t("fileIndex.title"),
        properties: {
          manual: {
            render() {
              return <FileIndexManualBuild />;
            },
            type: "action" as const,
            title: "",
            description: t("fileIndex.description"),
            actions: [{
              title: t("fileIndex.startBuild"),
            }],
          },
          [Config.ENABLE_LOCAL_AGENT]: {
            type: "boolean" as const,
            title: t("fileIndex.autoEnable.title"),
            description: t("fileIndex.autoEnable.description"),
            default: true,
          },
          ignoreFolder: {
            type: "action" as const,
            title: t("fileIndex.ignoreFolder.title"),
            description: t("fileIndex.ignoreFolder.description"),
            actions: [{
              title: t("fileIndex.ignoreFolder.button"),
              action: () => kwaiPilotBridgeAPI.extensionIndexFile.$openIndexIgnore(),
            }],
          },
          maxSpaceSize: {
            type: "integer" as const,
            title: t("fileIndex.maxSpace.title"),
            renderRight: () => {
              return <MaxFileIndexSpace />;
            },
          },
        },
      },
    };
  },
  get rules() {
    return {
      rules: {
        title: t("rules.title"),
        properties: {
          personRule: {
            type: "action" as const,
            title: t("rules.personalRule.title"),
            description: t("rules.personalRule.description"),
            actions: [{
              title: t("rules.personalRule.button"),
              action: () => kwaiPilotBridgeAPI.extensionRules.$openUserRule(),
            }],
          },
          projectRule: {
            type: "action" as const,
            title: t("rules.projectRule.title"),
            description: t("rules.projectRule.description"),
            actions: [{
              title: t("rules.projectRule.button"),
              action: () => kwaiPilotBridgeAPI.extensionRules.$openProjectRules(),
            }],
          },
        },
      },
    };
  },
};

export default CommonSettings;
export const titleClassName = "text-[22px] font-medium leading-[51px] text-[var(--vscode-foreground)]";
export const propertyTitleClassName = "text-[var(--vscode-foreground)] text-[13px] leading-[18px]";
export const descriptionClassName = "flex align-center text-[13px] leading-[18px] color-[var(--vscode-button-secondaryForeground)] opacity-50";
export const propertyPanelClassName = "bg-[var(--vscode-settings-rowHoverBackground)] rounded-[8px] p-[16px] flex flex-col";
export const borderClassName = "border-b border-[var(--vscode-editorGroup-border)] ";
