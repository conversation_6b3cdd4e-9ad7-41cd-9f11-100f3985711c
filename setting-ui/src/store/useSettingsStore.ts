import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { Config, StateReturnType, defaultConfig } from "shared/lib/state-manager/types";

type State = StateReturnType["config"];

type Action = {
  [K in keyof State as `set${Capitalize<string & K>}`]: (value: State[K]) => void;
};
export const userSettingsStore = create(
  immer<State & Action>(set => ({
    [Config.PROXY_URL]: defaultConfig[Config.PROXY_URL],
    setProxy(value: string) {
      set((state) => {
        state[Config.PROXY_URL] = value;
      });
    },

    [Config.MODEL_TYPE]: defaultConfig[Config.MODEL_TYPE],
    setModelType(value: string) {
      set((state) => {
        state[Config.MODEL_TYPE] = value;
      });
    },
    [Config.ENABLE]: defaultConfig[Config.ENABLE],
    setEnable(value: boolean) {
      set((state) => {
        state[Config.ENABLE] = value;
      });
    },
    [Config.COMMENT_COMPLETION_ENABLE]: defaultConfig[Config.COMMENT_COMPLETION_ENABLE],
    setCommentCompletionEnable(value: boolean) {
      set((state) => {
        state[Config.COMMENT_COMPLETION_ENABLE] = value;
      });
    },
    [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: defaultConfig[Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION],
    setMaxNewTokensForCodeCompletion(value: number) {
      set((state) => {
        state[Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION] = value;
      });
    },
    [Config.CODE_COMPLETION_DELAY]: defaultConfig[Config.CODE_COMPLETION_DELAY],
    setCodeCompletionDelay(value: number) {
      set((state) => {
        state[Config.CODE_COMPLETION_DELAY] = value;
      });
    },
    [Config.ENABLE_CODE_BLOCK_ACTION]: defaultConfig[Config.ENABLE_CODE_BLOCK_ACTION],
    setEnableCodeBlockAction(value: boolean) {
      set((state) => {
        state[Config.ENABLE_CODE_BLOCK_ACTION] = value;
      });
    },

    [Config.PREDICTION_ENABLE]: defaultConfig[Config.PREDICTION_ENABLE],
    setEnablePrediction(value: boolean) {
      set((state) => {
        state[Config.PREDICTION_ENABLE] = value;
      });
    },
    [Config.ENABLE_LOCAL_AGENT]: defaultConfig[Config.ENABLE_LOCAL_AGENT],
    setEnableLocalAgent(value: boolean) {
      set((state) => {
        state[Config.ENABLE_LOCAL_AGENT] = value;
      });
    },
    [Config.AGENT_PREFERENCE]: defaultConfig[Config.AGENT_PREFERENCE],
    setAgentPreference(value: string) {
      set((state) => {
        state[Config.AGENT_PREFERENCE] = value;
      });
    },
    [Config.ENABLE_DIAGNOSTICS_CHECK]: defaultConfig[Config.ENABLE_DIAGNOSTICS_CHECK],
    setEnableDiagnosticsCheck(value: boolean) {
      set((state) => {
        state[Config.ENABLE_DIAGNOSTICS_CHECK] = value;
      });
    },
    [Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE]: defaultConfig[Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE],
    setComposerAutoRunCommandExclude(value: string[]) {
      set((state) => {
        state[Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE] = value;
      });
    },
    [Config.COMPOSER_ENABLE_AUTO_RUN]: defaultConfig[Config.COMPOSER_ENABLE_AUTO_RUN],
    setComposerEnableAutoRun(value: boolean) {
      set((state) => {
        state[Config.COMPOSER_ENABLE_AUTO_RUN] = value;
      });
    },
    [Config.COMPOSER_ENABLE_AUTO_RUN_MCP]: defaultConfig[Config.COMPOSER_ENABLE_AUTO_RUN_MCP],
    setComposerEnableAutoRunMcp(value: boolean) {
      set((state) => {
        state[Config.COMPOSER_ENABLE_AUTO_RUN_MCP] = value;
      });
    },
    [Config.KWAIPILOT_LANG]: window.editorLanguage,
    setKwaiPilotLang(value?: string) {
      set((state) => {
        state[Config.KWAIPILOT_LANG] = value;
      });
    },
  })),
);
