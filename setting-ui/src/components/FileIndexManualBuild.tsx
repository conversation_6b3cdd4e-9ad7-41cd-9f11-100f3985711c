import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect, useMemo, useState } from "react";
import { Icon } from "@iconify/react";
import { SettingDesc } from "./SettingDesc";
import { Button } from "@chakra-ui/react";
import { IndexState } from "shared";
import { propertyTitleClassName, descriptionClassName, borderClassName } from "../schema/common";
import { shortenPathString } from "../utils/string";
import { t } from "i18next";

export const FileIndexManualBuild = () => {
  const [progress, setProgress] = useState(0);
  const [isStarted, setIsStarted] = useState(false);
  const buildFinish = useMemo(() => progress >= 1, [progress]);
  const [message, setMessage] = useState("");
  const [paused, setPaused] = useState(false);
  const [buildStatus, setBuildStatus] = useState<IndexState["status"]>("paused");
  // progress保留到小数点后两位
  const showProgress = useMemo(() => (progress * 100).toFixed(0), [progress]);
  const [lastBuildTime, setLastBuildTime] = useState("");

  const startBuildIndex = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$startBuildIndex();
  };
  const deleteIndex = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$deleteIndex();
  };
  const stopIndexBuild = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$stopIndex();
  };

  useEffect(() => {
    const sus = kwaiPilotBridgeAPI.observableAPI.indexState().subscribe((state) => {
      setProgress(state.indexingProgress);
      setIsStarted(state.indexing);
      setLastBuildTime(state.lastBuildTime);
      setMessage(state.indexingMessage);
      // 修复暂停状态逻辑：只要status是paused就显示暂停状态
      setPaused(state.status === "paused" && state.pauseIndexManual);
      setBuildStatus(state.status);
    });

    return () => {
      sus.unsubscribe();
    };
  }, []);

  return (
    <>
      <div className={"pb-[16px] " + borderClassName}>
        <div className="flex justify-between gap-[40px]">
          <div>
            <div className={propertyTitleClassName}>{t("fileIndex.title")}</div>
            <div className={descriptionClassName}>
              {t("fileIndex.description")}
            </div>
          </div>
          <div className="flex gap-3 h-[34px]">
            {!isStarted && !buildFinish && (
              <Button
                variant="blueSolid"
                onClick={
                  startBuildIndex
                }
              >
                {t("fileIndex.startBuild")}
              </Button>
            )}
            { isStarted && !buildFinish
            && (
              <Button
                onClick={stopIndexBuild}
                variant="blueSolid"
                leftIcon={<Icon icon="codicon:close"></Icon>}
              >
                {t("fileIndex.cancelBuild")}
              </Button>
            )}
            {buildFinish && (
              <Button onClick={startBuildIndex} leftIcon={<Icon icon="codicon:debug-restart"></Icon>}>
                {t("fileIndex.rebuild")}
              </Button>
            )}
            {buildFinish && (
              <Button
                onClick={deleteIndex}
                leftIcon={<Icon icon="material-symbols:delete-outline"></Icon>}
              >
                {t("fileIndex.deleteIndex")}
              </Button>
            )}
          </div>
        </div>
        <div className="pt-4 pb-2">
          <div className="w-full h-1.5 bg-[var(--vscode-list-inactiveSelectionBackground)] rounded-[3px]">
            <div className={`w-full h-full  rounded-[3px] ${buildFinish ? "bg-[#00C2A5ff]" : "bg-[var(--vscode-progressBar-background)]"}`} style={{ width: `${showProgress}%` }}></div>
          </div>
        </div>
        <div className="flex justify-between gap-3">
          <SettingDesc>
            {
              buildStatus === "error"
                ? <Icon icon="ix:error-filled" className="text-[#E35151]" />
                : buildStatus === "indexed"
                  ? <Icon icon="mdi:success-circle" className="text-[#00C2A5ff]"></Icon>
                  : ""
            }
            {paused
              ? t("fileIndex.paused")
              : message
                ? <span title={message} className="inline-block whitespace-nowrap overflow-hidden text-ellipsis">{shortenPathString(message)}</span>
                : buildFinish
                  ? `${t("fileIndex.buildSuccess")} ${lastBuildTime}`
                  : isStarted ? t("fileIndex.building") : t("fileIndex.notBuilt")}
          </SettingDesc>
          <div>
            {showProgress}
            %
          </div>
        </div>
      </div>
    </>
  );
};
