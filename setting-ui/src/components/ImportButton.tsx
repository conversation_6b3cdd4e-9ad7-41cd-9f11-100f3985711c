import { But<PERSON>, <PERSON>over, <PERSON><PERSON>Trigger, PopoverContent } from "@chakra-ui/react";
import { useState } from "react";
import { Icon } from "@iconify/react";
import { t } from "i18next";

interface Props {
  productName: string;
  onConfirm: () => Promise<void>;
}

export default function ImportButton({ productName, onConfirm }: Props) {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const handlerClick = async () => {
    setLoading(true);
    setOpen(false);
    await onConfirm();
    setLoading(false);
  };

  if (loading) return <Button isLoading={loading} loadingText={t("import.loading")}></Button>;

  return (
    <Popover isOpen={open} onClose={() => setOpen(false)} preventOverflow={false} strategy="fixed" placement="bottom-end">
      <PopoverTrigger>
        <Button onClick={() => setOpen(true)}>
          {t("import.button", { 0: productName })}
        </Button>
      </PopoverTrigger>
      <PopoverContent bg="var(--vscode-editor-background)" padding="16px" borderRadius="4px" display="flex" flexDirection="column" minWidth="0" border="1px solid var(--vscode-pickerGroup-border)">
        {/* <PopoverArrow bg="var(--vscode-editor-background)" boxShadow="none" border="1px solid var(--vscode-pickerGroup-border)" borderTop="none" borderLeft="none" /> */}
        <div className="w-full flex items-center text-[var(--vscode-foreground)] text-[13px] font-[600] leading-[18px] mb-[8px] gap-[4px]">
          <Icon icon="codicon:info" fontSize="16px" />
          <span>
            {t("import.confirmTitle", {
              0: productName,
            })}
          </span>
        </div>
        <div className="w-full text-[var(--vscode-foreground)] text-[13px] leading-[18px] mb-[16px] text-left">{t("import.confirmMessage")}</div>
        <div className="w-full flex justify-end gap-[8px]">
          <Button onClick={() => setOpen(false)}>{t("import.cancel")}</Button>
          <Button variant="blueSolid" onClick={handlerClick}>{t("import.confirm")}</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
