import { Button } from "@chakra-ui/react";
import { kwaiPilotBridgeAPI } from "../bridge";
import { UserInfo } from "@shared/types";
import { useState, useEffect } from "react";
import { propertyTitleClassName, descriptionClassName } from "../schema/common";
import { getCurrentEnvIsInIDE } from "@/utils/ide";
import { t } from "i18next";

export const RenderAccount = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const host = getCurrentEnvIsInIDE() ? "ide" : "plugin";

  const logout = async () => {
    kwaiPilotBridgeAPI.extensionSettings.$logout(host);
  };

  const login = () => {
    kwaiPilotBridgeAPI.extensionSettings.$login(host);
  };

  useEffect(() => {
    kwaiPilotBridgeAPI.getAndWatchUserInfo((userInfo) => {
      setUserInfo(userInfo ?? null);
    });
  }, []);

  return (
    <div className="flex items-center justify-between">
      <div className={"flex flex-col gap-[4px]" + propertyTitleClassName}>
        <div>
          {t("basics.account.settings")}
        </div>
        <div className={"flex align-center" + descriptionClassName}>
          {t("basics.account.currentUser")}
          {userInfo?.displayName ?? t("basics.account.notLoggedIn")}
        </div>
      </div>
      {userInfo
        ? <Button onClick={logout}>{t("basics.account.logout")}</Button>
        : <Button onClick={login}>{t("basics.account.login")}</Button>}
    </div>
  );
};