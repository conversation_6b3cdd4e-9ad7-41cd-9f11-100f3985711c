// 该文件用于初始化 React 应用，将 App 组件渲染到页面的根元素上
import * as ReactDOM from "react-dom/client";
import App from "./App";
import { ChakraProvider } from "@chakra-ui/react";
import theme from "./utils/theme";
import i18n from "i18next";
import { en } from "./i18n/locales/en";
import { zh } from "./i18n/locales/zh";

i18n.init({
  resources: {
    en: {
      translation: en,
    },
    zh: {
      translation: zh,
    },
  },
  lng: window.editorLanguage,
  fallbackLng: "en",
  interpolation: {
    // escapeValue: false,
  },
}).then(() => {
  const rootElement = document.getElementById("root") as Element;
  ReactDOM.createRoot(rootElement).render(
    <ChakraProvider theme={theme}>
      <App />
    </ChakraProvider>,
  );
});
