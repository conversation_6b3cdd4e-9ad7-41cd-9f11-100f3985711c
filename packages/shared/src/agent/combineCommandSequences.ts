import { isTerminalMessage } from "./isToolMessage";
import { InternalLocalMessage, TerminalTextStructure } from "./types";

/**
 * Combines sequences of command and command_output messages in an array of LocalMessages.
 *
 * This function processes an array of LocalMessages objects, looking for sequences
 * where a 'command' message is followed by one or more 'command_output' messages.
 * When such a sequence is found, it combines them into a single message, merging
 * their text contents.
 *
 * @param messages - An array of LocalMessage objects to process.
 * @returns A new array of LocalMessage objects with command sequences combined.
 *
 * @example
 * const messages: LocalMessage[] = [
 *   { type: 'ask', ask: 'command', text: 'ls', ts: 1625097600000 },
 *   { type: 'ask', ask: 'command_output', text: 'file1.txt', ts: 1625097601000 },
 *   { type: 'ask', ask: 'command_output', text: 'file2.txt', ts: 1625097602000 }
 * ];
 * const result = simpleCombineCommandSequences(messages);
 * // Result: [{ type: 'ask', ask: 'command', text: 'ls\nfile1.txt\nfile2.txt', ts: 1625097600000 }]
 */
export function combineCommandSequences(messages: InternalLocalMessage[]): InternalLocalMessage[] {
  const combinedCommands: InternalLocalMessage[] = [];

  // First pass: combine commands with their outputs
  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    if (isTerminalMessage(message)) {
      const terminalInfo = JSON.parse(message.text || "{}") as TerminalTextStructure;
      // let combinedCommand = terminalInfo.command || "";
      let didAddOutput = false;
      let outputMessage: InternalLocalMessage | null = null;
      let j = i + 1;
      const baseCommand = (terminalInfo.command || "") + `\n${COMMAND_OUTPUT_STRING}`;
      let outputContent = "";
      while (j < messages.length) {
        if (messages[j].ask === "command" || messages[j].say === "command") {
          // Stop if we encounter the next command
          break;
        }
        if (messages[j].ask === "command_output" || messages[j].say === "command_output") {
          if (!didAddOutput) {
            // Add a newline before the first output
            // combinedCommand += `\n${COMMAND_OUTPUT_STRING}`;
            didAddOutput = true;
            outputMessage = messages[j];
          }
          // handle cases where we receive empty command_output (ie when extension is relinquishing control over exit command button)
          const output = messages[j].text || "";
          if (output.length > 0) {
            // combinedCommand += "\n" + output;
            outputContent = "\n" + output;
          }
        }
        if (messages[j].say === "command_status_check_result") {
          const { output } = JSON.parse(messages[j].text || "{}");
          if (output) {
            outputContent = "\n" + output;
          }
        }
        j++;
      }

      combinedCommands.push({
        ...message,
        text: JSON.stringify({
          ...terminalInfo,
          command: baseCommand + outputContent,
        }),
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore 非 human message 类型检查
        outputMessage: outputMessage,
      });

      i = j - 1; // Move to the index just before the next command or end of array
    }
  }

  // Second pass: remove command_outputs and replace original commands with combined ones
  return messages
    .filter(msg => !(msg.ask === "command_output" || msg.say === "command_output"))
    .map((msg) => {
      if (msg.ask === "command" || msg.say === "command") {
        const combinedCommand = combinedCommands.find(cmd => cmd.ts === msg.ts);
        return combinedCommand || msg;
      }
      return msg;
    });
}
export const COMMAND_OUTPUT_STRING = "Output:";
export const COMMAND_REQ_APP_STRING = "REQ_APP";
