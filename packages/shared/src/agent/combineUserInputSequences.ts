import { isUserInputMessage } from "./isToolMessage";
import { InternalLocalMessage } from "./types";

/**
 * Combines sequences of user_input and user_input_result messages in an array of LocalMessages.
 *
 * This function processes an array of LocalMessages objects, looking for sequences
 * where a 'user_input' message is followed by one or more 'user_input_result' messages.
 * When such a sequence is found, it combines them into a single message, merging
 * their text contents.
 *
 * @param messages - An array of LocalMessage objects to process.
 * @returns A new array of LocalMessage objects with user_input sequences combined.
 *
 * @example
 * const messages: LocalMessage[] = [
 *   { type: 'ask', ask: 'user_input', text: 'ls', ts: 1625097600000 },
 *   { type: 'ask', ask: 'user_input_result', text: 'file1.txt', ts: 1625097601000 },
 *   { type: 'ask', ask: 'user_input_result', text: 'file2.txt', ts: 1625097602000 }
 * ];
 * const result = simpleCombineMcpSequences(messages);
 * // Result: [{ type: 'ask', ask: 'user_input', text: 'ls\nfile1.txt\nfile2.txt', ts: 1625097600000 }]
 */
export function combineUserInputSequences(messages: InternalLocalMessage[]): InternalLocalMessage[] {
  const combinedMes: InternalLocalMessage[] = [];
  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    if (isUserInputMessage(message)) {
      const mcpInfo = JSON.parse(message.text || "{}") as { toolName: string; response: string; error: string; query: string };
      let mcpOutput = mcpInfo.response || "";
      let mcpError = mcpInfo.error || "";
      let j = i + 1;

      while (j < messages.length) {
        if (messages[j].type === "ask" && messages[j].ask === "user_input") {
          // Stop if we encounter the next command
          break;
        }
        if (messages[j].type === "say" && messages[j].say === "user_input_result") {
          const { response } = JSON.parse(messages[j].text || "{}") || "";
          if (response.length > 0) {
            mcpOutput += response + "\n";
          }
        }
        if (messages[j].type === "say" && messages[j].say === "tool_error") {
          const error = messages[j].text || "";
          mcpError = error;
        }
        j++;
      }

      combinedMes.push({
        ...message,
        text: JSON.stringify({
          ...mcpInfo,
          response: mcpOutput,
          error: mcpError,
        }),
      });

      i = j - 1; // Move to the index just before the next command or end of array
    }
  }

  // Second pass: remove use_mcp_tool_results and replace original commands with combined ones
  return messages
    .filter(msg => !(msg.type === "say" && msg.say === "user_input_result"))
    .map((msg) => {
      if (msg.type === "ask" && msg.ask === "user_input") {
        const combinedMcp = combinedMes.find(msg => msg.ts === msg.ts);
        return combinedMcp || msg;
      }
      return msg;
    });
}
