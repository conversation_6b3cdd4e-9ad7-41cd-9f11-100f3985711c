import { InternalLocalMessage, CommandStatusCheckStructure } from "./types";
import { isCommandStatusCheckMessage } from "./isToolMessage";

export const COMMAND_STATUS_CHECK_RESULT_STRING = "Result:";

/**
 * 将 command_status_check 和 command_status_check_result 消息合并
 * 类似于 combineCommandSequences 的实现
 */
export function combineCommandStatusCheckSequences(messages: InternalLocalMessage[]): InternalLocalMessage[] {
  const combinedChecks: InternalLocalMessage[] = [];

  // First pass: combine command_status_check with their results
  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    if (isCommandStatusCheckMessage(message) && (message.say === "command_status_check")) {
      const statusCheckInfo = JSON.parse(message.text || "{}") as CommandStatusCheckStructure;
      let combinedContent = JSON.stringify(statusCheckInfo);
      let didAddResult = false;
      let resultMessage: InternalLocalMessage | null = null;
      let j = i + 1;

      while (j < messages.length) {
        if (messages[j].say === "command_status_check") {
          // Stop if we encounter the next command_status_check
          break;
        }
        if (messages[j].say === "command_status_check_result") {
          if (!didAddResult) {
            // Add a marker before the first result
            combinedContent = JSON.stringify({
              ...statusCheckInfo,
              combined_result_marker: COMMAND_STATUS_CHECK_RESULT_STRING,
            });
            didAddResult = true;
            resultMessage = messages[j];
          }
          // Add the result content
          const result = messages[j].text || "";
          if (result.length > 0) {
            const currentContent = JSON.parse(combinedContent) as CommandStatusCheckStructure & {
              combined_result_marker?: string;
              result?: string;
            };
            currentContent.result = (currentContent.result || "") + (currentContent.result ? "\n" : "") + result;
            combinedContent = JSON.stringify(currentContent);
          }
        }
        j++;
      }

      combinedChecks.push({
        ...message,
        text: combinedContent,
        // 添加 resultMessage 引用，类似于 command 的 outputMessage
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore 非 human message 类型检查
        resultMessage: resultMessage,
      });

      i = j - 1; // Move to the index just before the next command_status_check or end of array
    }
  }

  // Second pass: remove command_status_check_result and replace original command_status_check with combined ones
  return messages
    .filter(msg => !(msg.say === "command_status_check_result"))
    .map((msg) => {
      if (msg.ask === "command_status_check" || msg.say === "command_status_check") {
        const combinedCheck = combinedChecks.find(check => check.ts === msg.ts);
        return combinedCheck || msg;
      }
      return msg;
    });
}
