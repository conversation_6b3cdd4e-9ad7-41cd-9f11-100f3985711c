import { EditFileRequest, EditFileResponse, ExecuteCommandResponse, LocalMessage, MessageParam, WebviewMessage, DiffSet, CommandStatusCheckResponse } from "../agent";
import { DeleteMcpServerParams, FetchMcpDetailByMarketParams, InstallMcpParams, MCPFeaturedServer, McpServer, McpServerChangeEventDetail, RepoIndexParam, RestartMcpServerParams, ToggleMcpServerParams } from "../mcp/types";
import { type IdeSettings } from "./types";

export type ResponseBase<T> = {
  status: "ok" | "failed";
  message?: string;
  data?: T;
  code?: number; // 0: 正确，其他code：就是错误码
};
type ChatHistory = {
  role: "user" | "assistant";
  content: string;
};
export type SearchSearchParams = {
  query: string;
  chatHistory: ChatHistory[];
  topK?: number;
  targetDirectory?: string[];
};
export enum RepoStatusEnum {
  SUCCESS = "0",
  MAX_INDEX_SIZE_EXCEEDED_5K = "1",
  MAX_INDEX_SIZE_EXCEEDED_10K = "2",
  INDEX_FAILED = "9999",
  NOT_GIT_REPO = "11",
}

export type ToIdeFromCoreProtocol = {
  "state/updatePort": [{
    port: number;
    host?: string;
  }, void];
  "state/ideState": [undefined, ResponseBase<null>];
  "config/getIdeSetting": [undefined, ResponseBase<IdeSettings>];
  "index/progress": [
    {
      progress: number;
      total: number;
      done: number;
      filepath: string;
      action: "INDEX_FILE" | "PROCESS_FILE" | "ERROR";
      message?: string;
    },
    undefined,
  ];
  "state/sendNotification": [
    {
      type: "error" | "warning" | "info";
      name: string;
      message: string;
    },
    undefined,
  ];
  "state/ideInfo": [undefined, ResponseBase<{
    pluginVersion: string;
    version: string;
    platform: IdePlatform;
    repoInfo: {
      git_url: string;
      dir_path: string;
      commit: string;
      branch: string;
    };
    userInfo: {
      name: string;
    };
    proxyUrl: string;
    cwd: string;
    device?: IdeDevice;
  }>];
  /* 新增 or 更新 一条对话数据 */
  "assistant/agent/message": [LocalMessage, void];
  "assistant/agent/messageList": [LocalMessage[], void];
  /* 同步对话列表数据api_conversation_history */
  "assistant/agent/apiConversationList": [MessageParam[], void];
  "assistant/agent/environment": [{ includeFileDetails: boolean }, Promise<ResponseBase<string>>];
  "assistant/agent/executeCommand": [{ command: string; is_background: boolean; ignore_output: boolean }, Promise<ResponseBase<ExecuteCommandResponse>>];
  "assistant/agent/commandStatusCheck": [{ check_duration: number }, Promise<ResponseBase<CommandStatusCheckResponse>>];
  "assistant/agent/editFile": [EditFileRequest, Promise<ResponseBase<EditFileResponse>>];
  "assistant/agent/writeToFile": [{ path: string; content: string; newFile: boolean }, Promise<ResponseBase<EditFileResponse>>];
  "mcp/mcpServerChange": [McpServerChangeEventDetail, void];
};

export type IdePlatform = "xcode" | "vscode" | "jetbrains" | "kwaipilot-ide";
export type IdeDevice = "kwaipilot-vscode" | "kwaipilot-ide" | "kwaipilot-xcode" | "kwaipilot-intellij";

export type ToCoreFromIdeProtocol = {
  // 健康检查接口
  "state/agentState": [undefined, ResponseBase<null>];
  "state/userLogin": [{ username: string }, ResponseBase<null>];

  // 索引相关接口
  "index/file": [{ file: string; action: "modify" | "delete" | "create" }, ResponseBase<null>];
  "index/build": [undefined, ResponseBase<boolean>];
  "index/pause": [undefined, ResponseBase<boolean>];
  "index/clearIndex": [undefined, ResponseBase<boolean>];
  "index/repoIndex": [RepoIndexParam | undefined, ResponseBase<boolean>];

  // MCP 相关接口
  "mcp/getSettingsPath": [undefined, ResponseBase<string>];
  "mcp/getAllMcpServers": [undefined, ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>];
  "mcp/toggleMcpServer": [ToggleMcpServerParams, ResponseBase<void>];
  "mcp/restartMcpServer": [RestartMcpServerParams, ResponseBase<void>];
  "mcp/deleteMcpServer": [DeleteMcpServerParams, ResponseBase<void>];
  "mcp/fetchAvailableMcpListByMarket": [undefined, ResponseBase<{ records: MCPFeaturedServer[] }>];
  "mcp/installMcp": [InstallMcpParams, ResponseBase<boolean>];
  "mcp/fetchMcpDetailByMarket": [FetchMcpDetailByMarketParams, ResponseBase<MCPFeaturedServer>];

  // 状态相关接口
  "state/checkRepoState": [undefined, ResponseBase<{
    id: number;
    repo: string;
    repoPath: string;
    branch: string;
    commitId: string;
    lastUpdateTime: number;
    createTime: number;
    total: number;
    done: number;
    progress: number;
    isBuilding: boolean;
    status: RepoStatusEnum;
    isPaused: boolean;
    message: string;
  } | null>];

  // 搜索相关接口
  "search/search": [
    SearchSearchParams,
    ResponseBase<any>,
  ];

  // 助理模式
  "assistant/agent/local": [WebviewMessage, ResponseBase<null>];
  // 助理模式
  "assistant/agent/getDiffSet": [{ sessionId: string; lhsHash: string; rhsHash?: string }, ResponseBase<DiffSet[]>];
  "rules/getRulesList": [{ rules: string[] }, ResponseBase<string[]>];
};

// IDE
export type ToIdeProtocol = ToIdeFromCoreProtocol;
export type FromIdeProtocol = ToCoreFromIdeProtocol;

// Core
export type ToCoreProtocol = ToCoreFromIdeProtocol;
export type FromCoreProtocol = ToIdeFromCoreProtocol;
