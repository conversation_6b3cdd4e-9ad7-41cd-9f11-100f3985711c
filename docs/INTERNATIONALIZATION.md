# Kwaipilot 项目多语言开发指南

## 概述

Kwaipilot 项目采用多层次的多语言支持架构，覆盖VSCode扩展命令、Webview界面和设置UI三个主要部分。本文档将详细介绍如何在项目中进行多语言开发。

## 架构总览

项目的多语言支持分为三个层次：

```mermaid
graph TB
    A[VSCode 扩展层] --> D[package.nls.json/zh.json]
    B[Webview UI 层] --> E[webview-ui/src/i18n/]
    C[设置 UI 层] --> F[setting-ui/src/i18n/]
    
    D --> G[扩展命令、菜单项]
    E --> H[聊天界面、智能体界面]
    F --> I[设置页面、配置界面]
```

## 1. VSCode扩展层多语言

### 文件结构
```
├── package.nls.json        # 英文资源文件
├── package.nls.zh.json     # 中文资源文件
└── l10n/
    └── bundle.l10n.zh-cn.json  # VSCode l10n标准格式
```

### 使用方式

在 `package.json` 中定义命令时，使用 `%` 引用翻译键：

```json
{
  "contributes": {
    "commands": [
      {
        "command": "kwaipilot.newComposer",
        "title": "%kwaipilot.newComposer.title%"
      }
    ]
  }
}
```

### 翻译文件格式

**package.nls.json (英文)**:
```json
{
  "kwaipilot.newComposer.title": "Open Code Agent",
  "kwaipilot.inlineChat.title": "Kwaipilot: Conversational Code Generation"
}
```

**package.nls.zh.json (中文)**:
```json
{
  "kwaipilot.newComposer.title": "打开代码智能体",
  "kwaipilot.inlineChat.title": "Kwaipilot：对话式生成代码"
}
```

## 2. Webview UI层多语言

### 文件结构
```
webview-ui/src/i18n/
├── index.ts                # 配置入口
└── locales/
    ├── en.ts              # 英文翻译
    └── zh.ts              # 中文翻译
```

### 配置文件

**webview-ui/src/i18n/index.ts**:
```typescript
import { en } from "./locales/en";
import { zh } from "./locales/zh";

export const config = {
  resources: {
    en: {
      translation: en,
    },
    zh: {
      translation: zh,
    },
  },
  interpolation: {
    escapeValue: false,
  },
};
```

### 翻译文件格式

**locales/zh.ts** (中文翻译):
```typescript
export const zh = {
  // 通用
  "common.confirm": "确认",
  "common.cancel": "取消",
  "common.loading": "加载中...",
  
  // 聊天相关
  "chat.regenerate": "重新生成",
  "chat.thinking": "思考中",
  "chat.generating": "生成中",
  
  // 参数化翻译
  "chat.foundDocuments": "找到 {count} 个相关文档",
  "mcp.tooManyTools": "已启用 {{0}} 个工具",
};
```

**locales/en.ts** (英文翻译):
```typescript
export const en = {
  // Common
  "common.confirm": "Confirm",
  "common.cancel": "Cancel",
  "common.loading": "Loading...",
  
  // Chat related
  "chat.regenerate": "Regenerate",
  "chat.thinking": "Thinking",
  "chat.generating": "Generating",
  
  // Parameterized translation
  "chat.foundDocuments": "Found {count} related documents",
  "mcp.tooManyTools": "{{0}} tools enabled",
};
```

### 在组件中使用

```typescript
import { t } from 'i18next';

function MyComponent() {  
  return (
    <div>
      <h1>{t("chat.thinking")}</h1>
      <p>{t("chat.foundDocuments", { count: 5 })}</p>
      <button>{t("common.confirm")}</button>
    </div>
  );
}
```

## 3. 设置UI层多语言

### 文件结构
```
setting-ui/src/i18n/
└── locales/
    ├── en.ts              # 英文翻译
    └── zh.ts              # 中文翻译
```

### 翻译文件示例

**setting-ui/src/i18n/locales/zh.ts**:
```typescript
export const zh = {
  // 主导航标签
  "nav.basics": "基础",
  "nav.function": "功能",
  "nav.fileIndex": "代码索引",
  "nav.mcp": "MCP",
  "nav.rules": "规则配置",

  // 基础设置页面
  "basics.general.title": "通用",
  "basics.account.title": "账户",
  "basics.configMigration.title": "配置迁移",
  
  // 参数化翻译
  "import.button": "导入{{0}} 配置",
  "mcp.tooManyTools": "已启用 {{0}} 个工具，超40个工具会降低性能",
};
```

## 4. 翻译键命名规范

### 层级化命名
采用点分隔的层级命名方式：

```typescript
// 按功能模块分组
"nav.*"          // 导航相关
"basics.*"       // 基础设置
"function.*"     // 功能设置
"fileIndex.*"    // 文件索引
"mcp.*"          // MCP相关
"rules.*"        // 规则配置
"common.*"       // 通用文本

// 按UI组件分组
"chat.*"         // 聊天界面
"composer.*"     // 智能体界面
"history.*"      // 历史记录
"upload.*"       // 上传功能
"terminal.*"     // 终端相关
```

### 命名最佳实践

1. **描述性命名**: 键名应该清楚描述内容用途
```typescript
// ✅ 好的命名
"fileIndex.startBuild": "开始构建"
"mcp.status.available": "可使用"

// ❌ 避免的命名
"btn1": "开始构建"
"status1": "可使用"
```

2. **一致性**: 同类功能使用相同的命名模式
```typescript
// ✅ 一致的命名
"autoRun.confirmTitle": "切换为自动运行"
"autoRun.confirmMessage": "开启后，智能体将自动执行..."
"autoRun.confirmButton": "确认"

// ❌ 不一致的命名
"autoRun.title": "切换为自动运行" 
"autoRun.message": "开启后，智能体将自动执行..."
"autoRun.ok": "确认"
```

## 5. 参数化翻译

### 支持的参数格式

1. **数字占位符**: `{0}`, `{1}`, `{2}`
```typescript
"mcp.tooManyTools": "已启用 {{0}} 个工具"
// 使用: t("mcp.tooManyTools", "45")
```

2. **命名参数**: `{paramName}`
```typescript
"chat.foundDocuments": "找到 {count} 个相关文档"
// 使用: t("chat.foundDocuments", { count: 5 })
```

3. **复杂参数**: 支持多个参数
```typescript
"dialog.unprocessedChangesMessage": "有 {count} 个文件存在未处理的变更"
// 使用: t("dialog.unprocessedChangesMessage", { count: 3 })
```

## 6. 开发工作流程

### 添加新翻译的步骤

1. **确定翻译键名**
   - 按照命名规范选择合适的键名
   - 确保键名在所有语言文件中保持一致

2. **添加翻译内容**
   ```typescript
   // 中文 - webview-ui/src/i18n/locales/zh.ts
   "newFeature.title": "新功能标题",
   "newFeature.description": "这是新功能的描述",
   
   // 英文 - webview-ui/src/i18n/locales/en.ts  
   "newFeature.title": "New Feature Title",
   "newFeature.description": "This is the description of new feature",
   ```

3. **在组件中使用**
   ```typescript
   const { t } = useTranslation();
   return <h1>{t("newFeature.title")}</h1>;
   ```


## 8. 常见问题

### Q: 如何添加新的语言支持？
A: 
1. 在各个 `locales` 目录下添加新的语言文件
2. 在配置文件中注册新语言
3. 确保所有翻译键都有对应的翻译

## 10. 相关资源

- [i18next 官方文档](https://www.i18next.com/overview/getting-started)
- [VSCode 扩展国际化指南](https://code.visualstudio.com/api/references/extension-manifest#contributes.localizations)

## 11. 国际化业务逻辑

- 为了保证已有的插件用户的体验，所以插件侧依然是用中文
- IDE内根据用户的语言设置（config display language），来设置语言
- 在插件的开发环境，chat聊天部分一定是中文的，设置模块会跟随用户的设置，这是因为ide和vscode引入插件代码的方式不一样导致的
  - ide里使用webview-ui部分的代码，是用了该文件夹下的mount-export.ts文件中暴露的方法，可以通过ide将用户设置的语言传进来；vscode插件里使用的是改文件夹下的index.tsx文件，写死了语言是zh
  - setting-ui这部分始终是通过webview引入的，向window上注入了全局变量editorLanguage

多语言分为三个部分：
1.setting-ui和webview-ui的多语言 通过i18next实现
2.注册命令的多语言package.nls.json
3.service插件层的多语言,通过package.json文件中的l10n字段来配置的