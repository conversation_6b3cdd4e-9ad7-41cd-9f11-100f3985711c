import { create } from "zustand";
import path from "path-browserify";
import {
  CommandType,
  DisableRichEditorMenu,
  SharpCommand,
  SlashCommand,
} from "@shared/types";
import { RichEditorBoxPanelData, RichEditorBoxPanelDataCustomPrompt, RichEditorMenuType } from "@/components/TextArea/const";
import { RichEditorDisabledReason } from "@shared/constant";
import { CustomPromptData } from "shared/lib/CustomVariable";
import repoChatService from "@/services/repo-chat";
import { URI } from "vscode-uri";
import { isRuleFile } from "shared/lib/util";
import { Doc } from "shared/lib/business";
import { UriComponents } from "shared/lib/bridge/protocol";
import { t } from "i18next";

export interface MentionDefaultFile {
  uri: UriComponents;
  relativePath: string;
}

export interface RichEditorMenu {
  disabledMenu: DisableRichEditorMenu;
  sharpCommand: RichEditorBoxPanelData[];
  codeSearchDefaultFiles: MentionDefaultFile[];
  codeSearchDefaultDir: MentionDefaultFile[];
  customPrompts: RichEditorBoxPanelDataCustomPrompt[];
  ruleFiles: RichEditorBoxPanelData[];
  setDisabledMenu: (params: DisableRichEditorMenu) => void;
  setCodeSearchDefaultFiles: (files: MentionDefaultFile[]) => void;
  setCodeSearchDefaultDir: (files: MentionDefaultFile[]) => void;
  updateCurrentFilePath: (files: string) => void;
  updateCodeBasePath: (files: string) => void;
  setCustomPrompts: (prompts: CustomPromptData[]) => void;
  setRuleFiles: (files: string[]) => void;
  docList: Doc[];
  setDocList: (value: Doc[]) => void;
}

const filterPath = [".", "/"];

export const useRichEditPanelMenuStore = create<RichEditorMenu>((set, get) => {
  return {
    disabledMenu: {
      [SharpCommand.CURRENT_FILE]: {
        status: true,
        msg: RichEditorDisabledReason.unopenedFile,
      },
      [SharpCommand.FOLDER]: {
        status: false,
        msg: "",
      },
      [SharpCommand.FILE]: {
        status: true,
        msg: "",
      },
      [SharpCommand.CODEBASE]: {
        status: false,
        msg: "",
      },
      [SharpCommand.RULES]: {
        status: false,
        msg: "",
      },
      [SlashCommand.LINE_CODE_COMMENT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CODE_EXPLAIN]: {
        status: true,
        msg: "",
      },
      [SlashCommand.FUNC_COMMENT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CODE_REFACTOR]: {
        status: true,
        msg: "",
      },
      [SlashCommand.UNIT_TEST]: {
        status: true,
        msg: "",
      },
      [SlashCommand.FUNC_SPLIT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CLEAR_CONVERSATION]: {
        status: false,
        msg: "",
      },
    },
    sharpCommand: [
      {
        key: SharpCommand.CURRENT_FILE,
        title: t("mention.currentFile"),
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.CODEBASE,
        title: t("mention.codebase"),
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.FOLDER,
        title: t("mention.folder"),
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.FOLDER,
        data: SharpCommand.FOLDER,
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.FILE,
        title: t("mention.file"),
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.FILE,
        data: SharpCommand.FOLDER,
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.RULES,
        title: t("mention.rule"),
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.RULES,
        data: SharpCommand.RULES,
        commandType: CommandType.SHARP,
        uri: "",
      },
    ],
    codeSearchDefaultFiles: [],
    codeSearchDefaultDir: [],
    codeSearchWorkspaceFiles: [],
    codeSearchWorkspaceDir: [],
    customPrompts: [],
    ruleFiles: [],

    setDisabledMenu(params: DisableRichEditorMenu) {
      set({
        disabledMenu: {
          ...get().disabledMenu,
          ...params,
        },
      });
    },
    setCodeSearchDefaultFiles(files: MentionDefaultFile[]) {
      files = files.filter(f => !filterPath.includes(f.relativePath) && !isRuleFile(f.relativePath));
      set({
        codeSearchDefaultFiles: files,
      });
    },
    setCodeSearchDefaultDir(dir: MentionDefaultFile[]) {
      dir = dir.filter(f => !filterPath.includes(f.relativePath));
      set({
        codeSearchDefaultDir: dir,
      });
    },

    updateCurrentFilePath(path: string) {
      const sharpCommand = get().sharpCommand;
      const current = sharpCommand.find(
        item => item.key === SharpCommand.CURRENT_FILE,
      );
      if (current) {
        current.data = path;
        set({
          sharpCommand,
        });
      }
    },
    updateCodeBasePath(path: string) {
      const sharpCommand = get().sharpCommand;
      const current = sharpCommand.find(
        item => item.key === SharpCommand.CODEBASE,
      );
      if (current) {
        current.data = path;
        set({
          sharpCommand,
        });
      }
    },
    setCustomPrompts(prompts: CustomPromptData[]) {
      set({
        customPrompts: prompts.map<RichEditorBoxPanelDataCustomPrompt>(i => ({
          key: SlashCommand.CUSTOM_PROMPT,
          title: i.name,
          description: i.content,
          type: "customPrompt",
          data: i.content,
          commandType: CommandType.SLASH,
          raw: i,
          uri: "",
        })),
      });
    },
    setRuleFiles(files: string[]) {
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.RULES,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        ruleFiles: data,
      });
    },
    docList: [],
    setDocList: (value: Doc[]) => {
      set({
        docList: value,
      });
    },
  };
});
