import { createM<PERSON>ory<PERSON><PERSON><PERSON>, isRouteErrorResponse, Navigate, useRouteError } from "react-router-dom";
import { Chat } from "@/pages/chat";
import { InlineChat } from "@/pages/inline-chat";
import History from "@/pages/history";
import { PageComposer } from "@/pages/composer";
import { PageComposerV2 } from "@/pages/composer-v2";
import { Button } from "@chakra-ui/react";
import { useDesignToken } from "@/hooks/useDesignToken";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { setGlobalObject } from "@/utils/globalObject";
import { t } from "i18next";

function ErrorBoundary() {
  const error = useRouteError();
  const message = isRouteErrorResponse(error)
    ? error.status + " " + error.statusText
    : error instanceof Error
      ? error.message
      : JSON.stringify(error);
  const stack = error instanceof Error ? error.stack : null;
  const lightgrey = "rgba(200,200,200, 0.5)";
  const preStyles = {
    padding: "0.5rem",
    backgroundColor: lightgrey,
  };
  const { tokens } = useDesignToken();

  return (
    <div className=" px-2 py-3">
      <h2 className=" text-base font-medium text-text-common-primary">{t("error.applicationError")}</h2>
      <p className=" text-sm text-text-common-secondary">
        {t("error.contactSupport")}
        <Button variant="link" onClick={() => kwaiPilotBridgeAPI.extensionDeveloper.$reloadWebview()} fontSize={14} color={tokens.colorTextBrandDefault}>
          {t("error.refreshPage")}
        </Button>
      </p>
      <h3 className=" text-text-common-secondary mt-2" style={{ fontStyle: "italic" }}>{message}</h3>
      {stack ? <pre className=" overflow-auto" style={preStyles}>{stack}</pre> : null}
    </div>
  );
}
export const router = createMemoryRouter(
  [
    {
      path: "chat",
      element: <Chat />,
      errorElement: <ErrorBoundary />,
    },
    {
      path: "/",
      element: <Navigate to="/composer-v2" replace />,
      errorElement: <ErrorBoundary />,
    },
    {
      path: "inline-chat",
      element: <InlineChat />,
      errorElement: <ErrorBoundary />,
    },
    {
      path: "history",
      element: <History />,
      errorElement: <ErrorBoundary />,
    },
    {
      path: "composer",
      element: <PageComposer />,
      errorElement: <ErrorBoundary />,
    },
    {
      path: "composer-v2",
      element: <PageComposerV2 />,
      errorElement: <ErrorBoundary />,
    },
  ],
  { initialEntries: [window.__KWAIPILOT_RESTORE_ROUTE__ ?? "/composer-v2"] },
);

setGlobalObject("router", router);
