// NOTE: ide 组件，不影响插件逻辑
import React, { useState, useEffect } from "react";
import * as ReactDOM from "react-dom/client";
import { ChakraProvider } from "@chakra-ui/react";
import AppWrapper from "@/AppWrapper"; // Updated from App to AppWrapper
import theme from "@/utils/theme";
import CodeImage from "@/components/Prediction/codeImage";
import { IDENotLogged } from "@/components/IDENotLogged";
import { IdeEnvProvider } from "@/providers/IdeEnvProvider";
import { getCurrentEnvIsInIDE } from "./utils/ide";
import { VSCodeNativeBridge } from "@/bridge-export/kwaipilotBridge";
import { VSCodeServicesAccessor } from "./mount-export";
import { ConfigProvider as AntConfigProvider } from "antd";
import ErrorBoundary from "./components/ErrorBoundary";
import DefaultFallback from "./components/ErrorBoundary/DefaultFallback";

const IDEAPP = ({ accessor }: { accessor: VSCodeServicesAccessor }) => {
  const [userInfo, setUserInfo] = useState<any | null>(null);

  window.globalRadar = accessor.radar;

  useEffect(() => {
    accessor.userInfoWatcherService?.getAndWatchUserInfo((userInfo) => {
      setUserInfo(userInfo);
    });
  }, [accessor.userInfoWatcherService]);

  if (!userInfo) {
    return (
      <AntConfigProvider
        theme={{
          token: {
            fontFamily: "var(--vscode-font-family)",
          },
        }}
      >
        <ChakraProvider theme={theme}>
          <IdeEnvProvider
            value={{ isKwaiPilotIDE: getCurrentEnvIsInIDE(), accessor }}
          >
            <IDENotLogged onLogin={() => {
              accessor.loginService?.login();
            }}
            />
          </IdeEnvProvider>
        </ChakraProvider>
      </AntConfigProvider>
    );
  }

  return (
    <ErrorBoundary fallback={(error, resetError) => <DefaultFallback error={error} resetError={resetError} />}>
      <AntConfigProvider
        theme={{
          token: {
            fontFamily: "var(--vscode-font-family)",
          },
        }}
      >
        <ChakraProvider theme={theme}>
          <IdeEnvProvider
            value={{ isKwaiPilotIDE: getCurrentEnvIsInIDE() }}
          >
            <CodeImage />
            <AppWrapper />
          </IdeEnvProvider>
        </ChakraProvider>
      </AntConfigProvider>
    </ErrorBoundary>
  );
};

/**
 * 挂载应用到指定DOM元素
 *
 * @param rootElement - 要挂载应用的DOM元素
 * @param bridge - 桥接对象实现
 * @param accessor - 可选的 VSCode services accessor，用于访问 VSCode 服务
 * @returns 包含重新渲染和销毁方法的对象
 */
const mountApp = async (
  rootElement: HTMLElement,
  bridge: VSCodeNativeBridge,
  accessor?: VSCodeServicesAccessor,
) => {
  if (typeof document === "undefined") {
    console.error("mount.tsx error: document was undefined");
    return {
      rerender: () => {},
      dispose: () => {},
    };
  }

  const root = ReactDOM.createRoot(rootElement);

  const rerender = () => {
    accessor?.sideActionService?.mountUI({
      uiType: "chat",
      params: {},
    });

    root.render(
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      <IDEAPP accessor={accessor!} />,
    );
  };

  const dispose = () => {
    root.unmount();
    accessor?.sideActionService?.unmountUI();
    bridge.dispose();
  };

  rerender();

  return {
    rerender,
    dispose,
  };
};

export default mountApp;
