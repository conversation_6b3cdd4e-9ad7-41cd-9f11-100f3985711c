import { kwaiPilotBridgeAPI } from "@/bridge";
import { useDesignToken } from "@/hooks/useDesignToken";
import { Box, Link, BoxProps } from "@chakra-ui/react";
import { t } from "i18next";

export function NotLoggedHint(props: BoxProps) {
  const { tokens } = useDesignToken();
  return (
    <Box p={2} pt={6} {...props}>
      {t("login.greeting")}
      <br />
      <Link
        textDecoration="none"
        onClick={() => kwaiPilotBridgeAPI.login()}
        target="javascript:void(0)"
        className="text-blue-400 mt-[8px] inline-block"
        color={tokens.colorTextBrandDefault}
      >
        {t("login.loginAction")}
      </Link>
      <br />
      <Link
        onClick={() => {
          kwaiPilotBridgeAPI.openUrl(
            "https://docs.corp.kuaishou.com/k/home/<USER>/fcAAvma1OGznlywMIiYfgHz6w",
          );
        }}
        target="javascript:void(0)"
        className="text-blue-400 mt-[8px] inline-block"
        color={tokens.colorTextBrandDefault}
        textDecoration="none"
      >
        {t("login.manual")}
      </Link>
    </Box>
  );
}
