import { useEffect, useRef, forwardRef, useState, useMemo, useCallback } from "react";
import "xterm/css/xterm.css";
import "./styles.css";

import { terminal } from "@/components/Terminal/index";
import { getHighLight } from "../Dialog/Highlight";
import { useShikiTheme } from "@/hooks/useShikiTheme";
import { useTrustedHTML } from "@/hooks/useTrustedHTML";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { CustomScrollBar } from "../CustomScrollbar";

export interface EditableTerminalRef {
  getCommand: () => string;
  insertChar: (char: string) => void;
  insertText: (text: string) => void;
  deleteChar: () => void;
  clear: () => void;
  replaceContent: (content: string) => void; // 用于初始化或强制替换内容
  moveCursorLeft: () => void;
  moveCursorRight: () => void;
  getCursorPosition: () => number;
  focus: () => void;
}

interface EditableTerminalProps {
  output: string;
  commandCancelClicked: boolean;

}

export const EditableTerminal = forwardRef<EditableTerminalRef, EditableTerminalProps>(({ output, commandCancelClicked }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [showStatic, setShowStatic] = useState(true);
  const [currentOutput, setCurrentOutput] = useState(output);
  const editorConfig = useVsEditorConfig(state => state.editorConfig);

  const handleFallbackToStatic = useCallback(() => {
    // 确保使用最新的output内容
    setCurrentOutput(output);
    setShowStatic(true);
  }, [output]);

  // 终端渲染逻辑
  useEffect(() => {
    let isMounted = true;

    const renderTerminal = async () => {
      try {
        const ter = await terminal.getTerminal();

        // 检查组件是否还在挂载状态
        if (!isMounted || !terminalRef.current) {
          return;
        }

        // 检查终端容器是否存在且终端没有在当前容器中渲染
        if (!ter.isRenderingIn(terminalRef.current)) {
          console.log("aaaaaaaaaa, setShowStatic: false, rendering terminal");
          ter.render(terminalRef.current, handleFallbackToStatic);
          if (isMounted) {
            setShowStatic(false);
          }
        }
      }
      catch (error) {
        console.error("Failed to render terminal:", error);
        // 渲染失败时回退到静态显示
        if (isMounted) {
          setShowStatic(true);
        }
      }
    };

    renderTerminal();

    return () => {
      isMounted = false;
    };
  }, [handleFallbackToStatic]);

  // 当output变化且当前显示静态内容时，确保使用最新的output
  useEffect(() => {
    if (showStatic && output !== currentOutput) {
      setCurrentOutput(output);
    }
  }, [output, showStatic, currentOutput]);

  const theme = useShikiTheme();

  const html = useMemo(() => getHighLight({
    code: currentOutput,
    language: "shellscript",
    theme,
    transformers: [
      {
        pre(node) {
          node.properties.style = `background-color: transparent`;
        },
        code(node) {
          node.properties.style = `background-color: transparent;font-family: ${editorConfig.fontFamily};`;
        },
      },
    ],
  }), [currentOutput, theme, editorConfig.fontFamily]);

  const elementRef = useTrustedHTML(html);

  if ((commandCancelClicked || currentOutput) && showStatic) {
    return (
      <CustomScrollBar className="p-1.5 bg-editor-background max-h-[220px]">
        <div ref={elementRef} />
      </CustomScrollBar>
    );
  }

  return (
    <div
      ref={terminalRef}
      className="editable-terminal"

      style={{
        overflow: "hidden",
        overflowX: "scroll",
        display: showStatic ? "none" : "block",
      }}
    />
  );
});

EditableTerminal.displayName = "EditableTerminal";
