/* EditableTerminal 样式文件 */
.editable-terminal {
  background: var(--vscode-editor-background, #1e1e21);
  position: relative;
  padding: 6px !important;
}

/* 滚动条始终存在但默认透明，保持占位 */
.editable-terminal::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.editable-terminal::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.editable-terminal::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.editable-terminal::-webkit-scrollbar-thumb:hover {
  background: transparent;
}

/* 悬停或聚焦时显示滚动条 */
.editable-terminal:hover::-webkit-scrollbar-track,
.editable-terminal:focus-within::-webkit-scrollbar-track {
  background: transparent;
}

.editable-terminal:hover::-webkit-scrollbar-thumb,
.editable-terminal:focus-within::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-activeBackground, rgba(191, 191, 191, 0.4));
}

.editable-terminal:hover::-webkit-scrollbar-thumb:hover,
.editable-terminal:focus-within::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground, rgba(191, 191, 191, 0.7));
}

/* Firefox 滚动条处理 */
.editable-terminal {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.editable-terminal:hover,
.editable-terminal:focus-within {
  scrollbar-color: rgba(191, 191, 191, 0.4) transparent;
}
.editable-terminal.read-only {
  background: var(--vscode-input-background, #2d2d30);
  border-color: var(--vscode-input-border, #3c3c3c);
  cursor: default;
  opacity: 0.8;
}

/* xterm.js 覆盖样式 */
.editable-terminal .xterm {
  background: transparent !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  min-height: 16px;
}

.editable-terminal .xterm-viewport {
  background: transparent !important;
  overflow: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.editable-terminal .xterm-screen {
  background: transparent !important;
  display: flex;
  align-items: center;
}

.editable-terminal .xterm-cursor-layer {
  background: transparent !important;
}

.editable-terminal .xterm-cursor {
  background: var(--vscode-editorCursor-foreground, #ffffff) !important;
}

.editable-terminal:not(:focus-within) .xterm-cursor {
  background: transparent !important;
  border: none !important;
  animation: none !important;
  opacity: 1 !important;
}
.editable-terminal.read-only .xterm-cursor {
  display: none !important;
  /* 只读模式下隐藏光标 */
}

.editable-terminal .xterm-rows {
  color: var(--vscode-editor-foreground, #cccccc);
  white-space: nowrap;
  overflow: hidden;
}

.editable-terminal .xterm-selection {
  background: var(--vscode-editor-selectionBackground, #264f78) !important;
}
/* 禁用滚动条 */
.editable-terminal .xterm-viewport::-webkit-scrollbar {
  display: none;
}