// FIXME: must Canvas2D: Multiple readback operations using getImageData are faster with the willReadFrequently attribute set to true
// https://github.com/xtermjs/xterm.js/issues/4260
import { Terminal as XTerminal, IDisposable, ITheme } from "xterm";
import "xterm/css/xterm.css";
// import "./styles.css"; // 引入我们的自定义样式
import { FitAddon } from "xterm-addon-fit";
import { WebLinksAddon } from "xterm-addon-web-links";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { WebviewTerminalShape } from "shared/lib/bridge/protocol";
import { DOM } from "@/utils/dom";
import { createDeferred, Deferred } from "shared/lib/util";
import { WebglAddon } from "xterm-addon-webgl";

export interface TerminalRenderContext {
  container: HTMLElement;
  onFallbackToStatic: () => void;
}

export class UITerminal {
  private readonly fitAddon = new FitAddon();
  private readonly xTerm: XTerminal;
  private readonly xTermOnKeyDisposables: IDisposable[] = [];
  private $terminal: HTMLDivElement;
  private currentRenderContext: TerminalRenderContext | null = null;

  write(data: string): void {
    this.xTerm.write(data);
  }

  // 获取终端的当前内容
  getTerminalContent(startLine: number): string {
    const buffer = this.xTerm.buffer.active;
    const lines: string[] = [];

    // 获取所有非空行
    for (let i = startLine; i < buffer.length; i++) {
      const line = buffer.getLine(i);
      if (line) {
        const lineText = line.translateToString().trimEnd();
        lines.push(lineText);
      }
    }

    // 移除尾部的空行
    while (lines.length > 0 && lines[lines.length - 1] === "") {
      lines.pop();
    }

    return lines.join("\n");
  }

  getCurrentLineNumber(): number {
    const buffer = this.xTerm.buffer.active;
    // NOTE: +1 展示命令的行不需要，在上面会有单独的行展示
    return buffer.baseY + buffer.cursorY;
  }

  private pty = {
    write: (key: string) => {
      kwaiPilotBridgeAPI.extensionTerminal.$writeToTerminal(this.pid, key);
    },
    updateScreenSize: (_size: { cols: number; rows: number }) => {
      // TODO: 实现屏幕尺寸更新逻辑
    },
  };

  render($container: HTMLElement, onFallbackToStatic?: () => void) {
    // 如果之前已经渲染到其他位置，通知旧位置回退到静态显示
    if (this.currentRenderContext && this.currentRenderContext.container !== $container) {
      this.currentRenderContext.onFallbackToStatic();
    }

    // 更新当前渲染上下文
    this.currentRenderContext = {
      container: $container,
      onFallbackToStatic: onFallbackToStatic || (() => { }),
    };

    $container.appendChild(this.$terminal);
  }

  // 检查是否正在指定容器中渲染
  isRenderingIn($container: HTMLElement): boolean {
    return this.currentRenderContext?.container === $container;
  }

  updateTheme(theme: ITheme) {
    this.xTerm.options.theme = theme;
  }

  constructor(private readonly pid: number, theme: ITheme) {
    const $terminal = DOM.createElement("div") as HTMLDivElement;
    this.$terminal = $terminal;
    $terminal.style.boxSizing = "content-box";
    const computedStyle = getComputedStyle(DOM.getRootContainer());
    const fontFamily = computedStyle.getPropertyValue("--vscode-editor-font-family");
    this.xTerm = new XTerminal({
      cursorBlink: false,
      cursorStyle: "block",
      fontFamily,
      convertEol: true,
      disableStdin: false,
      fontSize: 12,
      cols: 100,
      rows: 14,
      scrollback: 10000, // 增加到10000行历史记录
      allowTransparency: true,
      theme,
    });
    const fitAddon = this.fitAddon;
    this.xTerm.loadAddon(fitAddon);
    this.xTerm.loadAddon(new WebLinksAddon());
    this.xTerm.loadAddon(new WebglAddon());
    this.xTerm.open($terminal);
    this.xTerm.attachCustomKeyEventHandler(this.onCustomKeyEvent);
    this.xTermOnKeyDisposables.push(
      this.xTerm.onKey(this.onKey),
      this.xTerm.onResize(this.onResize),
    );
    this.fit();
  }

  fit() {
    this.fitAddon.fit();
  }

  dispose() {
    this.xTermOnKeyDisposables.forEach(i => i.dispose());
  }

  private onKey = (e: { domEvent: KeyboardEvent; key: string }) => {
    this.pty.write(e.key);
  };

  private onResize = (e: { cols: number; rows: number }) => {
    // 更新终端的屏幕字符宽度
    this.pty.updateScreenSize({
      cols: e.cols,
      rows: e.rows,
    });
  };

  private onCustomKeyEvent = (e: KeyboardEvent): boolean => {
    // 一个 key 会触发 keydown keypress keyup，只处理一个
    if (e.type !== "keydown") {
      return true;
    }
    // Ctrl 组合键处理
    if (e.ctrlKey && !e.metaKey && !e.altKey) {
      // Ctrl+A - 行首
      if (e.key === "a") {
        this.pty.write("\x01");
        return false;
      }

      // Ctrl+B - 向后移动一个字符
      if (e.key === "b") {
        this.pty.write("\x02");
        return false;
      }

      // Ctrl+C - 中断信号
      if (e.key === "c") {
        this.pty.write("\x03");
        return false;
      }

      // Ctrl+D - EOF/删除字符
      if (e.key === "d") {
        this.pty.write("\x04");
        return false;
      }
      // Ctrl+E - 行尾
      if (e.key === "e") {
        this.pty.write("\x05");
        return false;
      }

      // Ctrl+F - 向前移动一个字符
      if (e.key === "f") {
        this.pty.write("\x06");
        return false;
      }

      // Ctrl+G - 响铃
      if (e.key === "g") {
        this.pty.write("\x07");
        return false;
      }

      // Ctrl+H - 退格
      if (e.key === "h") {
        this.pty.write("\x08");
        return false;
      }

      // Ctrl+I - 制表符 (Tab)
      if (e.key === "i") {
        this.pty.write("\x09");
        return false;
      }

      // Ctrl+J - 换行
      if (e.key === "j") {
        this.pty.write("\x0A");
        return false;
      }

      // Ctrl+K - 删除到行尾
      if (e.key === "k") {
        this.pty.write("\x0B");
        return false;
      }
      // Ctrl+L - 清屏
      if (e.key === "l") {
        this.pty.write("\x0C");
        return false;
      }

      // Ctrl+M - 回车
      if (e.key === "m") {
        this.pty.write("\x0D");
        return false;
      }
      // Ctrl+N - 下一行/下一条历史命令
      if (e.key === "n") {
        this.pty.write("\x0E");
        return false;
      }

      // Ctrl+O - 换行后执行
      if (e.key === "o") {
        this.pty.write("\x0F");
        return false;
      }

      // Ctrl+P - 上一行/上一条历史命令
      if (e.key === "p") {
        this.pty.write("\x10");
        return false;
      }

      // Ctrl+Q - 继续输出 (XON)
      if (e.key === "q") {
        this.pty.write("\x11");
        return false;
      }

      // Ctrl+R - 反向搜索历史
      if (e.key === "r") {
        this.pty.write("\x12");
        return false;
      }

      // Ctrl+S - 停止输出 (XOFF)
      if (e.key === "s") {
        this.pty.write("\x13");
        return false;
      }

      // Ctrl+T - 交换字符
      if (e.key === "t") {
        this.pty.write("\x14");
        return false;
      }

      // Ctrl+U - 删除到行首
      if (e.key === "u") {
        this.pty.write("\x15");
        return false;
      }

      // Ctrl+V - 插入下一个字符
      if (e.key === "v") {
        this.pty.write("\x16");
        return false;
      }

      // Ctrl+W - 删除前一个单词
      if (e.key === "w") {
        this.pty.write("\x17");

        return false;
      }

      // Ctrl+X - 列表补全
      if (e.key === "x") {
        this.pty.write("\x18");
        return false;
      }

      // Ctrl+Y - 粘贴
      if (e.key === "y") {
        this.pty.write("\x19");
        return false;
      }

      // Ctrl+Z - 暂停进程
      if (e.key === "z") {
        this.pty.write("\x1A");
        return false;
      }
      // Ctrl+[ - ESC
      if (e.key === "[") {
        this.pty.write("\x1B");
        return false;
      }

      // Ctrl+\ - 退出信号
      if (e.key === "\\") {
        this.pty.write("\x1C");
        return false;
      }

      // Ctrl+] - 组分隔符
      if (e.key === "]") {
        this.pty.write("\x1D");
        return false;
      }
      // Ctrl+^ - 记录分隔符
      if (e.key === "^" || e.key === "6") {
        this.pty.write("\x1E");
        return false;
      }

      // Ctrl+_ - 单元分隔符/撤销
      if (e.key === "_" || e.key === "-") {
        this.pty.write("\x1F");
        return false;
      }
    }

    // Meta (macOS Cmd) 组合键处理
    if (e.metaKey && !e.ctrlKey) {
      // Cmd+K - 清空控制台
      if (e.key === "k") {
        this.pty.write("\x0C"); // Ctrl+L 的控制字符
        return false;
      }
      // Cmd+V - 粘贴 (可选择发送控制字符或直接粘贴)
      if (e.key === "v") {
        navigator.permissions.query({ name: "clipboard-read", allowWithoutGesture: false } as any)
          .then(() => navigator.clipboard.readText())
          .then((text) => {
            this.pty.write(text);
          });

        return false;
      }
      // Cmd+左箭头 - 去行首
      if (e.key === "ArrowLeft") {
        this.pty.write("\x01"); // Ctrl+A
        return false;
      }

      // Cmd+右箭头 - 去行尾
      if (e.key === "ArrowRight") {
        this.pty.write("\x05"); // Ctrl+E
        return false;
      }

      // Cmd+上箭头 - 向上翻页
      if (e.key === "ArrowUp") {
        this.pty.write("\x1B[5~"); // Page Up
        return false;
      }

      // Cmd+下箭头 - 向下翻页
      if (e.key === "ArrowDown") {
        this.pty.write("\x1B[6~"); // Page Down
        return false;
      }

      // Cmd+Backspace - 删除前一个单词
      if (e.key === "Backspace") {
        this.pty.write("\x15"); // Ctrl+U
        return false;
      }
    }

    // Alt 组合键处理
    if (e.altKey && !e.ctrlKey && !e.metaKey) {
      // Alt+B - 向后移动一个单词
      if (e.key === "b") {
        this.pty.write("\x1Bb");
        return false;
      }

      // Alt+F - 向前移动一个单词
      if (e.key === "f") {
        this.pty.write("\x1Bf");
        return false;
      }

      // Alt+D - 删除下一个单词
      if (e.key === "d") {
        this.pty.write("\x1Bd");
        return false;
      }

      // Alt+Backspace - 删除前一个单词
      if (e.key === "Backspace") {
        this.pty.write("\x17"); // Ctrl+W
        return false;
      }

      // Alt+左箭头 - 向后移动一个单词
      if (e.key === "ArrowLeft") {
        this.pty.write("\x1Bb");
        return false;
      }

      // Alt+右箭头 - 向前移动一个单词
      if (e.key === "ArrowRight") {
        this.pty.write("\x1Bf");
        return false;
      }
    }

    return true;
  };
}

export class TerminalManager implements WebviewTerminalShape {
  private terminals = new Map<number, UITerminal>();
  private renderDeferred: Deferred<UITerminal> | null = null;
  constructor() {

  }

  $write(pid: number, data: string) {
    const uiterminal = this.terminals.get(pid);
    if (uiterminal) {
      uiterminal.write(data);
      return true;
    }
    return false;
  }

  $getTerminalContent(pid: number, startLinenumber: number): string {
    const uiterminal = this.terminals.get(pid);
    return uiterminal?.getTerminalContent(startLinenumber) ?? "";
  }

  $getCurrentLinenumber(pid: number): number {
    const uiterminal = this.terminals.get(pid);
    return uiterminal?.getCurrentLineNumber() ?? 0;
  }

  $renderUiTerminal(pid: number): void {
    const terminal = this.terminals.get(pid) ?? new UITerminal(pid, this.getTheme());
    this.terminals.set(pid, terminal);
    console.log("wwwwwwwwwwwwww: 解决了一个 deferred", pid);
    this.renderDeferred?.resolve(terminal);
  }

  async getTerminal() {
    // 这里如果直接过去pid，pid不一定是最新的pid，因为点击执行后需要 agent 给插件层发消息采取判断是否更新pid
    this.renderDeferred = createDeferred<UITerminal>();
    console.log("wwwwwwwwwwwwww: 创建了一个 deferred");
    return await this.renderDeferred.promise;
  }

  async disposeTerminalIfNotIsBackground() {
    const pids = await kwaiPilotBridgeAPI.extensionTerminal.$disposeTerminalIfNotIsBackground();
    pids.forEach((pid) => {
      const terminal = this.terminals.get(pid);
      if (terminal) {
        terminal.dispose();
        this.terminals.delete(pid);
      }
    });
  }

  showTerminal(pid?: number) {
    kwaiPilotBridgeAPI.extensionTerminal.$showTerminal(pid);
  }

  private getTheme(): ITheme {
    const root = DOM.getBodyContainer();
    const getToken = (name: string) => getComputedStyle(root).getPropertyValue(name);
    return {
      background: getToken("--vscode-editor-background"),
      foreground: getToken("--vscode-terminal-foreground"),
      black: getToken("--vscode-terminal-ansiBlack"),
      red: getToken("--vscode-terminal-ansiRed"),
      green: getToken("--vscode-terminal-ansiGreen"),
      yellow: getToken("--vscode-terminal-ansiYellow"),
      blue: getToken("--vscode-terminal-ansiBlue"),
      magenta: getToken("--vscode-terminal-ansiMagenta"),
      cyan: getToken("--vscode-terminal-ansiCyan"),
      white: getToken("--vscode-terminal-ansiWhite"),
      brightBlack: getToken("--vscode-terminal-ansiBrightBlack"),
      brightRed: getToken("--vscode-terminal-ansiBrightRed"),
      brightGreen: getToken("--vscode-terminal-ansiBrightGreen"),
      brightYellow: getToken("--vscode-terminal-ansiBrightYellow"),
      brightBlue: getToken("--vscode-terminal-ansiBrightBlue"),
      brightMagenta: getToken("--vscode-terminal-ansiBrightMagenta"),
      brightCyan: getToken("--vscode-terminal-ansiBrightCyan"),
      brightWhite: getToken("--vscode-terminal-ansiBrightWhite"),
    };
  }

  freshTerminalTheme() {
    const theme = this.getTheme();
    this.terminals.forEach((ter) => {
      ter.updateTheme(theme);
    });
  }
}

export const terminal = new TerminalManager();
