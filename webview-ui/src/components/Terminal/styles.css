/* Terminal 组件样式 - 使用CSS变量设置xterm.js主题 */

/* 基础样式 */
.xterm-rows {
  color: var(--vscode-terminal-foreground) !important;
}

/* 光标样式 */
.xterm-cursor {
  color: var(--vscode-terminalCursor-background) !important;
  background-color: var(--vscode-terminalCursor-foreground) !important;
}

/* 选择区域样式 */
.xterm-selection {
  background-color: var(--vscode-terminal-selectionBackground) !important;
}

.xterm-selected {
  color: var(--vscode-terminal-selectionForeground) !important;
}

/* ANSI 颜色配置 */
.xterm-fg-0 {
  color: var(--vscode-terminal-ansiBlack) !important;
}

.xterm-fg-1 {
  color: var(--vscode-terminal-ansiRed) !important;
}

.xterm-fg-2 {
  color: var(--vscode-terminal-ansiGreen) !important;
}

.xterm-fg-3 {
  color: var(--vscode-terminal-ansiYellow) !important;
}

.xterm-fg-4 {
  color: var(--vscode-terminal-ansiBlue) !important;
}

.xterm-fg-5 {
  color: var(--vscode-terminal-ansiMagenta) !important;
}

.xterm-fg-6 {
  color: var(--vscode-terminal-ansiCyan) !important;
}

.xterm-fg-7 {
  color: var(--vscode-terminal-ansiWhite) !important;
}

/* ANSI 亮色配置 */
.xterm-fg-8 {
  color: var(--vscode-terminal-ansiBrightBlack) !important;
}

.xterm-fg-9 {
  color: var(--vscode-terminal-ansiBrightRed) !important;
}

.xterm-fg-10 {
  color: var(--vscode-terminal-ansiBrightGreen) !important;
}

.xterm-fg-11 {
  color: var(--vscode-terminal-ansiBrightYellow) !important;
}

.xterm-fg-12 {
  color: var(--vscode-terminal-ansiBrightBlue) !important;
}

.xterm-fg-13 {
  color: var(--vscode-terminal-ansiBrightMagenta) !important;
}

.xterm-fg-14 {
  color: var(--vscode-terminal-ansiBrightCyan) !important;
}

.xterm-fg-15 {
  color: var(--vscode-terminal-ansiBrightWhite) !important;
}

/* 背景色配置 */
.xterm-bg-0 {
  background-color: var(--vscode-terminal-ansiBlack) !important;
}

.xterm-bg-1 {
  background-color: var(--vscode-terminal-ansiRed) !important;
}

.xterm-bg-2 {
  background-color: var(--vscode-terminal-ansiGreen) !important;
}

.xterm-bg-3 {
  background-color: var(--vscode-terminal-ansiYellow) !important;
}

.xterm-bg-4 {
  background-color: var(--vscode-terminal-ansiBlue) !important;
}

.xterm-bg-5 {
  background-color: var(--vscode-terminal-ansiMagenta) !important;
}

.xterm-bg-6 {
  background-color: var(--vscode-terminal-ansiCyan) !important;
}

.xterm-bg-7 {
  background-color: var(--vscode-terminal-ansiWhite) !important;
}

.xterm-bg-8 {
  background-color: var(--vscode-terminal-ansiBrightBlack) !important;
}

.xterm-bg-9 {
  background-color: var(--vscode-terminal-ansiBrightRed) !important;
}

.xterm-bg-10 {
  background-color: var(--vscode-terminal-ansiBrightGreen) !important;
}

.xterm-bg-11 {
  background-color: var(--vscode-terminal-ansiBrightYellow) !important;
}

.xterm-bg-12 {
  background-color: var(--vscode-terminal-ansiBrightBlue) !important;
}

.xterm-bg-13 {
  background-color: var(--vscode-terminal-ansiBrightMagenta) !important;
}

.xterm-bg-14 {
  background-color: var(--vscode-terminal-ansiBrightCyan) !important;
}

.xterm-bg-15 {
  background-color: var(--vscode-terminal-ansiBrightWhite) !important;
}

/* 非激活状态的选择区域 */
.xterm-inactive-selection {
  background-color: var(--vscode-terminal-inactiveSelectionBackground) !important;
}

/* 查找匹配高亮 */
.xterm-find-match {
  background-color: var(--vscode-terminal-findMatchBackground) !important;
}

.xterm-find-match-highlight {
  background-color: var(--vscode-terminal-findMatchHighlightBackground) !important;
}

/* 悬停高亮 */
.xterm-hover-highlight {
  background-color: var(--vscode-terminal-hoverHighlightBackground) !important;
}

/* 边框样式 */
.xterm-viewport {
  border: var(--vscode-terminal-border) !important;
}

/* 滚动条样式 */
.xterm-viewport::-webkit-scrollbar {
  width: 4px;
}

.xterm-viewport::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

.xterm-viewport::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 4px;
}

.xterm-viewport::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}