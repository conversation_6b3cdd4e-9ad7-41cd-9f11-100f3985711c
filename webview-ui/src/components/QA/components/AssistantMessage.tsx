import { ICachedMessage } from "@/utils/sessionUtils";
import { Actions } from "./Actions";
import { useEffect, useMemo, useState } from "react";
import { useRecordStore } from "@/store/record";
import { StreamLoading } from "@/components/Streamerloading";
import { Flex, Spinner } from "@chakra-ui/react";

import { MarkdownRender } from "./MarkdownRender";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { IChatModelType } from "@shared/types/business";
import { ReportOpt } from "@shared/types/logger";
import { reportCopyMessage } from "@/http/api/feedback";
import { ICachedMessageAnswer, QAItem } from "@shared/types/chatHistory";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Icon } from "@/components/Union/t-iconify";
import clsx from "clsx";
import { GlowingText } from "@/components/ui/GlowingText";
import { AUTO_THINK_MODEL_SET, THINK_MODEL_SET } from "shared/lib/business";
import { t } from "i18next";

type AssisantMessageProps = {
  data: ICachedMessageAnswer[];
  onResend: (modelType?: IChatModelType) => void;
  onContinue: () => void;
  onLikeOrUnlike: (item: ICachedMessage, isLike?: boolean) => void;
  isLast?: boolean;
  qaItem: QAItem;
};

export const AssisantMessage = (props: AssisantMessageProps) => {
  const { data: answers, onLikeOrUnlike, onResend, isLast, qaItem, onContinue } = props;
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  // 数据index
  const total = answers.length;
  const [currentIndex, setCurrentIndex] = useState<number>(total - 1);
  const currentAnswer = answers[currentIndex];

  useEffect(() => {
    setCurrentIndex(answers.length - 1);
  }, [answers.length]);
  const handleNext = () => {
    setCurrentIndex(val => (val + 1) % total);
  };
  const handlePre = () => {
    setCurrentIndex(val => (val - 1 + total) % total);
  };
  const handleCopy = (item: ICachedMessageAnswer) => {
    const { reply, question, id } = item;
    const text = item.thinkInfo ? item.thinkInfo.content : reply || "";
    kwaiPilotBridgeAPI.copyToClipboard(text.replace(/\n$/, ""));
    reportCopyMessage({
      isBlock: false,
      reply: text,
      chatId: id,
      question,
    });
    const parms: ReportOpt<"copy"> = {
      key: "copy",
      type: "llmMsg",
    };
    reportUserAction(parms, answers[0].id);
    collectClick("VS_COPY_RESPONSE");
  };

  // 当前加载中的数据是否和我有关？
  const isLoading = !!(
    loadingStatu
    && loadingStatu.id === currentAnswer.id
    && loadingStatu.status === "loading"
  );
  // 有内容返回之前
  const renderPreposition = () => {
    if (AUTO_THINK_MODEL_SET.has(currentAnswer.modelType) || THINK_MODEL_SET.has(currentAnswer.modelType)) {
      return (
        <div className={clsx("py-[3px] mt-2 px-3 flex items-center gap-[2px] text-[11px]")}>
          <Icon icon="codicon:chevron-right" width="12" height="12" />
          <GlowingText>
            {AUTO_THINK_MODEL_SET.has(currentAnswer.modelType) ? t("chat.analyzing") : t("chat.thinkingInProgress")}
          </GlowingText>
        </div>
      );
    }
    return (
      <div className="p-[12px]">
        <Flex align="center" gap={2.5}>
          <Spinner size="xs"></Spinner>
          {t("chat.thinkingInProgress")}
        </Flex>
      </div>
    );
  };

  // 使用 useMemo 缓存渲染结果
  const memoizedContent = useMemo(
    () => (
      <div className=" overflow-y-hidden">
        <MarkdownRender answer={currentAnswer} qaItem={qaItem} />
      </div>
    ),
    [currentAnswer, qaItem],
  );

  return (
    <div className="flex flex-col w-full gap-[4px] overflow-y-hidden rounded-[8px]" key={answers[currentIndex].id}>
      <StreamLoading
        isLoading={isLoading}
        id={currentAnswer.id}
        className="flex"
        key={currentAnswer.id}
      >
        {currentAnswer?.reply
          ? memoizedContent
          : isLoading
            ? renderPreposition()
            : <div className=" text-descriptionForeground text-[12px] px-2 py-2">模型返回内容为空</div>}
      </StreamLoading>
      {!isLoading && currentAnswer?.reply && (
        <Actions
          currentAnswer={currentAnswer}
          total={total}
          current={currentIndex + 1}
          handleNext={handleNext}
          handlePre={handlePre}
          onLikeOrUnlike={isLike => onLikeOrUnlike(currentAnswer, isLike)}
          onResend={onResend}
          onContinue={onContinue}
          likeStatu={currentAnswer?.likeStatus}
          onCopy={() => handleCopy(currentAnswer)}
          isLast={isLast}
        />
      )}
    </div>
  );
};
