import React, { useCallback } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useComposerState } from "@/logics/composer/context/ComposerStateContext";
import { Switch, Box, Text, useColorMode } from "@chakra-ui/react";
import { Tooltip } from "@/components/Union/chakra-ui";
import { generateCustomUUID } from "@/utils/sessionUtils";
import { createPlainTextEditorState } from "@/components/TextArea/lexical/editorState";
import { t } from "i18next";

export interface FocusModeToggleProps {
  className?: string;
}

/**
 * 专注模式切换组件
 * 开启时会新开一个 tab，关闭时也会新开一个 tab
 */
export const FocusModeToggle: React.FC<FocusModeToggleProps> = ({ className }) => {
  const { focusMode, toggleFocusMode, localMessages, sessionId } = useComposerState();
  const { colorMode } = useColorMode();

  const isInWelcomeMode = localMessages.length === 0;
  const handleToggle = useCallback((checked: boolean) => {
    toggleFocusMode(checked);
    if (checked) {
      const conversationId = generateCustomUUID();

      const question = `Analyze this repository and create basic abstract rules that would help guide an AI assistant.

Abstract documents are markdown files that are always in '.kwaipilot/abstract'.

Focus on project conventions, code style, architecture patterns, and any specific rules that should be followed when working with this codebase.

For the initial setup, please only create the following files:
  - product.md: Short summary of the product
  - tech.md: Build system used, tech stack, libraries, frameworks etc. If there are any common commands for building, testing, compiling etc make sure to include a section for that
  - structure.md: Project organization and folder structure
  
You do not need to create any folders. They have been created for you.

The goal is to be succinct, but capture information that will be useful for an LLM application operating in this project.
`;
      kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
        type: "newTask",
        // 这个字段其实已经没用了
        task: question,
        reqData: {
          sessionId: sessionId,
          chatId: conversationId,
        },
        rules: [],
        editorState: createPlainTextEditorState(question),
        questionForHumanReading: question,
        contextItems: [],
        editingMessageTs: undefined,
      });
    }
  }, [sessionId, toggleFocusMode]);

  if (!isInWelcomeMode) {
    return focusMode ? t("composer.deepMode") : "普通模式";
  }

  return (
    <Tooltip
      label={focusMode ? "关闭专注模式" : "开启专注模式"}
      placement="top"
    >
      <Box
        className={className}
        display="flex"
        alignItems="center"
        gap={2}
        cursor="pointer"
        onClick={() => handleToggle(!focusMode)}
        borderRadius="md"
        _hover={{
          bg: colorMode === "dark" ? "gray.700" : "gray.100",
        }}
      >
        <Text
          className=" cursor-pointer leading-[18px] inline-block text-foreground text-[13px] py-[3px]"
        >
          {t("composer.deepMode")}
        </Text>

        <Switch
          isChecked={focusMode}
          onChange={(e) => {
            e.stopPropagation();
            handleToggle(e.target.checked);
          }}
          size="sm"
          colorScheme="blue"
        />
      </Box>
    </Tooltip>
  );
};

export default FocusModeToggle;
