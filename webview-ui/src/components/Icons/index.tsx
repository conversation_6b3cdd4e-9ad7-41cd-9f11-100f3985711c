// export const IconTerminal = ({ className }: { className: string }) => {
//   return (
//     <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
//       <path d="M5.02929 5.828L2.38229 8.474L5.02929 11.12L4.32129 11.828L1.32129 8.828V8.12L4.32129 5.12L5.02929 5.828ZM12.0293 5.12L11.3213 5.828L13.9683 8.474L11.3213 11.12L12.0293 11.828L15.0293 8.828V8.12L12.0293 5.12ZM5.22929 13.25L6.12329 13.698L11.1233 3.698L10.2293 3.25L5.22929 13.25Z" fill="currentColor" />
//     </svg>
//   );
// };

export const IconCopy = ({ className }: { className?: string }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" className={className}>
      <g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
        <path d="m4 4l1-1h5.414L14 6.586V14l-1 1H5l-1-1zm9 3l-3-3H5v10h8z" />
        <path d="M3 1L2 2v10l1 1V2h6.414l-1-1z" />
      </g>
    </svg>
  );
};

export const IconCheck = ({ className }: { className?: string }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" className={className}><path fill="currentColor" fill-rule="evenodd" d="m14.431 3.323l-8.47 10l-.79-.036l-3.35-4.77l.818-.574l2.978 4.24l8.051-9.506z" clip-rule="evenodd" /></svg>
  );
};

export const IconTerminal = ({ className }: { className: string }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" className={className}>
      <g fill="currentColor">
        <path fill-rule="evenodd" d="M1.5 3L3 1.5h18L22.5 3v18L21 22.5H3L1.5 21zM3 3v18h18V3z" clip-rule="evenodd" />
        <path d="M7.06 7.5L6 8.56l4.243 4.243L6 17.046l1.06 1.06L12 13.168v-.728zm4.94 9h6V18h-6z" />
      </g>
    </svg>
  );
};

export const IconLineExternal = ({ className, onClick }: { className: string; onClick: React.MouseEventHandler<SVGSVGElement> }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" onClick={onClick} className={className}>
      <g fill="currentColor">
        <path d="M1.5 1H6v1H2v12h12v-4h1v4.5l-.5.5h-13l-.5-.5v-13z" />
        <path d="M15 1.5V8h-1V2.707L7.243 9.465l-.707-.708L13.293 2H8V1h6.5z" />
      </g>
    </svg>
  );
};

export const IconChevronDown = ({ className }: { className: string }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" className={className}><path fill="currentColor" fill-rule="evenodd" d="m7.976 10.072l4.357-4.357l.62.618L8.284 11h-.618L3 6.333l.619-.618z" clip-rule="evenodd" /></svg>
  );
};
