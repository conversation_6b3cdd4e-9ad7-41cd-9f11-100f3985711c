import clsx from "clsx";
import { ReactNode } from "react";
import { t } from "i18next";

interface EmptyTextProps {
  subMenuTitle?: string;
  isSecondary?: boolean;
  title?: ReactNode;
}

export const EmptyText: React.FC<EmptyTextProps> = ({ subMenuTitle, isSecondary, title }) => {
  return (
    <div
      className={clsx(
        "group gap-6 flex items-center px-3 w-full bg-bg-fill",
        [subMenuTitle ? "h-[28px]" : "h-[42px]"],
        {
          "justify-between": isSecondary,
        },
      )}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
    >
      <div className={clsx("truncate", "text-text-common-primary")}>
        {title ?? t("mention.noMatch")}
      </div>
    </div>
  );
};
