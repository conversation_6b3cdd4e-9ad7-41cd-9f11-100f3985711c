import LogoDark from "@/assets/images/logo-dark.svg?react";
import LogoLight from "@/assets/images/logo-light.svg?react";
import LogoTextIcon from "@/assets/images/logo-text.svg?react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useColorMode } from "@chakra-ui/react";
import { t } from "i18next";

export interface IMainLogoProps {
  className?: string;
}

export const MainLogo = (props: IMainLogoProps) => {
  const { colorMode: theme } = useColorMode();
  const { className } = props;

  return (
    <div
      className={`flex px-0 pb-0 flex-col items-center shrink-0 flex-nowrap relative ${
        className || ""
      }`}
    >
      <div
        className="w-[66px] h-[66px] relative flex-col justify-start items-start inline-flex cursor-pointer"
        onClick={() => {
          const homeUrl = "https://kwaipilot.corp.kuaishou.com/chat";
          kwaiPilotBridgeAPI.openUrl(homeUrl);
        }}
      >
        {theme === "dark" ? <LogoDark /> : <LogoLight />}
      </div>
      <div className="flex mt-[27px] flex-col gap-[2px] items-center shrink-0 flex-nowrap relative">
        <div className="h-[20px] shrink-0" style={{ transform: "scale(1.25)" }}>
          <LogoTextIcon className={`icon-fill-${theme}`} />
        </div>
        <span
          className="text-tab-inactiveForeground h-[18px] shrink-0 basis-auto  text-[14px] font-normal leading-[18px] relative text-center"
        >
          {t("logo.subtitle")}
        </span>
      </div>
    </div>
  );
};
