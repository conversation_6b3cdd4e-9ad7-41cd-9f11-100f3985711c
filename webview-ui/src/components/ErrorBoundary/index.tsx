import { captureException } from "@/utils/weblogger";
import { Component, ErrorInfo, ReactNode } from "react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode | ((error: Error, resetError: () => void) => ReactNode);
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染显示错误 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, _errorInfo: ErrorInfo): void {
    // 将错误上报到 radar
    captureException(error);
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    if (hasError && error) {
      if (typeof fallback === "function") {
        return fallback(error, this.resetError);
      }

      if (fallback) {
        return fallback;
      }

      return (
        <div
          style={{
            padding: "20px",
            margin: "10px",
            border: "1px solid #f5222d",
            borderRadius: "4px",
            backgroundColor: "rgba(245, 34, 45, 0.05)",
          }}
        >
          <h2 style={{ color: "#f5222d" }}>出错了</h2>
          <p>应用遇到了一个问题，我们已经记录了这个错误。</p>
          <details style={{ whiteSpace: "pre-wrap", marginTop: "10px" }}>
            <summary>错误详情</summary>
            <p>{error.message}</p>
            <small>{error.stack}</small>
          </details>
          <button
            onClick={this.resetError}
            style={{
              marginTop: "10px",
              padding: "4px 12px",
              backgroundColor: "#1890ff",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            重试
          </button>
        </div>
      );
    }

    return children;
  }
}

export default ErrorBoundary;
