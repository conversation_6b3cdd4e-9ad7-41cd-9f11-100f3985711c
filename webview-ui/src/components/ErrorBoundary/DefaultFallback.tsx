import React from "react";
import { logger } from "@/utils/logger";

interface DefaultFallbackProps {
  error: Error;
  resetError: () => void;
}

const DefaultFallback: React.FC<DefaultFallbackProps> = ({ error, resetError }) => {
  return (
    <div
      style={{
        padding: "20px",
        margin: "10px",
        border: "1px solid #f5222d",
        borderRadius: "4px",
        backgroundColor: "rgba(245, 34, 45, 0.05)",
      }}
    >
      <h2 style={{ color: "#f5222d" }}>出错了</h2>
      <p>应用遇到了一个问题，我们已经记录了这个错误并会尽快修复。</p>
      <details style={{ whiteSpace: "pre-wrap", marginTop: "10px" }}>
        <summary>错误详情</summary>
        <p>{error.message}</p>
        <small>{error.stack}</small>
      </details>
      <button
        onClick={() => {
          logger.info("用户点击了错误回退UI中的重试按钮", "DefaultFallback");
          resetError();
        }}
        style={{
          marginTop: "10px",
          padding: "4px 12px",
          backgroundColor: "#1890ff",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        重试
      </button>
    </div>
  );
};

export default DefaultFallback;
