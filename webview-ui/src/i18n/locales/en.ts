export const en = {
  // Common
  "common.confirm": "Confirm",
  "common.cancel": "Cancel",
  "common.copy": "Copy",
  "common.copied": "Copied",
  "common.loading": "Loading...",
  "common.error": "Error",
  "common.success": "Success",
  "common.noResults": "No results",
  "common.searchNoResults": "No search results",
  "common.continue": "Continue",
  "common.accept": "Accept",
  "common.reject": "Reject",
  "common.run": "Run",
  "common.cancel_action": "Cancel",
  "common.parameters": "Parameters",
  "common.result": "Result",
  "common.file": "File",
  "common.add": "Add",
  "common.view": "View",
  "common.open": "Open",
  "common.management": "Management",
  "common.market": "Market",
  "common.beta": "Beta", // Added

  // Errors and status
  "error.applicationError": "Application Error",
  "error.contactSupport":
    "Please provide the following information to Kwaipilot support, or",
  "error.refreshPage": "Refresh Page",
  "error.loadingFailed": "Loading Failed",
  "error.imageLoadFailed": "Image loading failed",
  "error.fileEditFailed": "File editing failed",
  "error.toolCallFailed": "Tool call failed",
  "error.runFailed": "Execution failed",
  "error.noCheckpoint": "No related checkpoint found",
  "error.noMessage": "No related message found",
  "error.renderingError": "Rendering Error",
  "error.editorRenderingError":
    "Editor rendering error occurred, please ensure the plugin version is up to date",
  "error.rulesTimeout": "Rules retrieval timeout",
  "error.submitError": "Submission process error",
  "error.contactOncall": "please contact kwaipilot oncall",
  "error.getCurrentFileSelectionFailed": "Failed to get current file selection", // Added

  "message.orphaned":
    "The above messages could not find their belonging conversation. Contact oncall to resolve",
  "message.detached":
    "The above messages could not find their belonging conversation",

  // Login and account
  "login.greeting":
    "Hi, I'm Kwaipilot, the AI tool that best understands Kuaishou development. Log in now to start your AI programming journey!",
  "login.loginAction": "Login to Kwaipilot",
  "login.manual": "View Kwaipilot User Manual",
  "login.pleaseLogin": "Please login first",
  "login.copySuccess": "Copy successful",
  "login.sessionIdCopied": "Session ID copied to clipboard {{id}}",
  "login.chatIdCopied": "Chat ID copied to clipboard {{id}}",

  // History
  "history.currentWorkspace": "Current Workspace",
  "history.otherWorkspace": "Other Workspaces",
  "history.allShown": "All shown",
  "history.delete": "Delete",
  "history.noHistory": "No history records",
  "history.clear": "Clear History", "history.edit": "Edit",
  "history.confirm": "Confirm",
  "history.current": "· Current",

  // Chat interface
  "chat.stopGeneration": "Stop Generation",
  "chat.thinking": "Thinking",
  "chat.generating": "Generating",
  "chat.applying": "Applying",
  "chat.waitingForOperation": "Waiting for operation",
  "chat.taskCompleted": "Task completed",
  "chat.executing": "Executing",
  "chat.codeGenerationStopped": "Code generation paused",
  "chat.composerModeHint":
    "Currently in assistant mode (beta). To start AI conversation, please create",
  "chat.placeholder1": "Feel free to ask me anything, use # to reference knowledge",
  "chat.placeholder":
    "Feel free to ask me anything, use # to reference knowledge, / to invoke commands",
  "chat.regenerate": "Regenerate", // Added
  "chat.previous": "Previous", // Added
  "chat.next": "Next", // Added
  "chat.foundDocuments": "Found {{count}} related documents", // Added
  "chat.generatingPleaseTryLater":
    "Generating in progress, please try again later", // Added

  // Input and mention functionality
  "mention.file": "Files",
  "mention.folder": "Folders",
  "mention.knowledge": "Knowledge Base",
  "mention.rule": "Rules",
  "mention.customPrompt": "Custom Prompts",
  "mention.addRule": "Add Rule",
  "mention.web": "Web",
  "mention.codebase": "Codebase",
  "mention.command": "Command",
  "mention.recent": "Recent",
  "mention.currentFile": "Current File",
  "mention.addProjectRule": "Add Project Rule",
  "mention.clickOrReturn": "Click or",
  "mention.return": "Return",
  "mention.noMatch": "No matching results",
  "mention.noResults": "No results",
  "mention.noCustomPrompts": "No custom prompts available, go to",
  "mention.promptManagement": "Prompt Management Platform",
  "mention.create": "Create",
  "mention.noRules": "No rules available",
  "mention.knowledgeOnlyOne": "Currently supports single selection only",
  "mention.inputPlaceholder": "Please enter",
  "mention.knowledgeEmptyText": "Knowledge",
  "mention.searchNoResults": "No search results",
  "mention.openAFolderFirst": "Pleace open a folder first",

  // Slash commands
  "slashCommand.funcComment": "Function Comment",
  "slashCommand.lineCodeComment": "Inline Comment",
  "slashCommand.codeExplain": "Code Explanation",
  "slashCommand.codeRefactor": "Code Optimization",
  "slashCommand.funcSplit": "Function Split",
  "slashCommand.unitTest": "Unit Test",
  "slashCommand.customPrompt": "Custom Prompt",
  "slashCommand.clearContext": "Clear Context",
  "slashCommand.command": "Command",

  // Composer page
  "composer.deepMode": "Deep Mode",
  "composer.moreCapabilities": "For more capabilities, see",
  "composer.userManual": "Kwaipilot User Manual",
  "composer.modifyCurrentPrompt": "Modify Current Chat Prompt",
  "composer.modifyCurrentPromptDesc":
    "Modify the prompt of the current chat session",
  "composer.openNewChatWithPrompt": "Open New Chat with Prompt",
  "composer.openNewChatWithPromptDesc":
    "Create a new chat session and set initial prompt",
  "composer.newSession": "New Session", // 新会话的英文翻译键
  "composer.notCurrentWorkspace": "Not Current Workspace Conversation",
  "composer.notCurrentWorkspaceDesc":
    "This session does not belong to the current workspace. To ensure your experience, you can",
  "composer.createNewSession": "Create new session",

  // MCP related
  "mcp.status.available": "Available",
  "mcp.status.preparing": "Preparing",
  "mcp.status.unavailable": "Unavailable",
  "mcp.configError": "MCP configuration file error",
  "mcp.noServers": "No MCP Servers",
  "mcp.management": "MCP Management",
  "mcp.market": "MCP Market",
  "mcp.riskCommand":
    "Agent determined this command is risky and requires manual confirmation",
  "mcp.blacklistCommand":
    "This command prefix is in the blacklist and cannot be executed automatically",
  "mcp.blacklistPrefix": "This command prefix is in",
  "mcp.blacklistSuffix": "in blacklist, cannot auto-execute", // Added

  // Code and file operations
  "code.copy": "Copy",
  "code.accept": "Accept",
  "code.reject": "Reject",
  "code.reapply": "Reapply",
  "code.showCodeView": "Show Code View",
  "code.showDiffView": "Show Diff View",
  "code.noChanges": "No change suggestions",
  "code.generating": "Generating",
  "code.applying": "Applying",
  "code.pendingReview": "Pending review",
  "code.totalChanges": "Total changes",
  "code.filesChanged": "files",
  "code.rejected": "(Rejected)",
  "code.rejectAll": "Reject All",
  "code.acceptAll": "Accept All",
  "code.indexNotComplete": "Code index build incomplete",
  "code.indexIncompleteDesc":
    "Current index build is incomplete, this may affect subsequent answer output quality. Click to view build progress",

  // Tool operations
  "tool.readTask": "Read Task",
  "tool.searchMemory": "Search Memory",
  "tool.saveMemory": "Save Memory",
  "tool.fileView": "File View",
  "tool.codeSearch": "Code Search",
  "tool.fileList": "File List Query",
  "tool.regexSearch": "Regex Search",
  "tool.writeFile": "Write File",
  "tool.commandExecution": "Command Execution",
  "tool.codebaseSearch": "Codebase Search",
  "tool.resultsFound": "related results found",

  // Auto-run settings
  "autorun.mode": "Auto Run",
  "autorun.askBeforeRun": "Ask Before Run",
  "autorun.switchToAutoRun": "Switch to Auto Run",
  "autorun.description":
    "When enabled, the agent will automatically execute commands except those in the blacklist",
  "autorun.mcpDescription": "and MCP",
  "autorun.securityWarning": ", please be aware of potential security risks",
  "autorun.mcpAutoRun":
    "When enabled, the agent will automatically execute MCP and commands except those in the blacklist. Please be aware of potential security risks",

  // Conversation and messages
  "conversation.confirmSend": "Are you sure you want to send this message?",
  "conversation.historyWillBeCleared":
    "Subsequent conversation history will be cleared. Continue?",
  "conversation.confirmSendTitle": "Confirm Send?",
  "conversation.restoreAndSend":
    "All code changes will be reverted to before this conversation occurred, and subsequent conversation history will be cleared. Continue?",
  "conversation.onlyModifyConversation": "Only Modify Conversation",
  "conversation.continueAndRestore": "Continue and Restore Code",
  "conversation.restoreToCurrentAnswer": "Restore to current answer?",
  "conversation.restoreWarning":
    "All code changes will be restored to before this version. Continue?",
  "conversation.noCheckpointSupport":
    "This conversation does not support rollback",
  "conversation.vscodeWorkspaceNotSupported":
    "Conversation rollback is not supported in VSCode workspace mode",
  "conversation.restoreToConversationStart":
    "Rollback to before this conversation started",
  "conversation.confirmRollback": "Confirm Rollback?", // Added
  "conversation.rollbackWarning":
    "All code changes will be rolled back to before this conversation occurred, and subsequent conversation history will be cleared. Continue?", // Added

  // Diagnostics and fixes
  "diagnostic.codeErrors": "code errors",
  "diagnostic.autoFixSettings": "Auto Fix Settings",
  "diagnostic.fix": "Fix",
  "diagnostic.fixErrors": "Fix these errors",
  "diagnostic.codeErrorsCount": "{{count}} code errors",

  // Other features
  "inlineChat.naturalLanguageCode": "Natural language code generation",
  "localService.connectionLost":
    "Local service connection lost. Please check if the local service is running normally",
  "upload.addImage": "Add Image",
  "upload.modelNotSupportImage":
    "Current model {{modelName}} does not support image uploads",
  "upload.supportedFiles":
    "Support uploading multiple files (total size not exceeding 3MB), supports .pdf, .docx, .txt code files, etc.",
  "upload.fileInfo": "Uploaded file information",
  "upload.UploadFileBtn": "Upload File",
  "upload.useChatSubmit": "Use Chat Submit",
  "artifact.previewTrigger": "Trigger preview",
  "artifact.thinkInfoCalculation": "Calculate thinkInfo",
  "artifact.stillThinking": "Still thinking, updating cost",
  "artifact.compatibilityNote":
    "Compatible with historical data without answerId",
  // Added
  "like.like": "Like",
  "like.dislike": "Dislike",
  "parsing.parsing": "Parsing",
  "parsing.filePathFailed": "Failed to get file path",
  "state.statusUpdate": "Status update",
  "tooltip.copy": "Copy",
  "tooltip.reject": "Reject",
  "tooltip.accept": "Accept",
  "tooltip.reapply": "Reapply",
  // Home page related
  "home.footer.help": "Help Documentation",
  "home.footer.bestPractice": "Best Practices",
  "home.footer.joinGroup": "Join User Group",
  "home.footer.moreProducts": "More Products",
  "home.footer.officialWebsite": "Kwaipilot Official Website",
  "home.footer.chatEngine": "Kwaipilot Intelligent Q&A Engine",
  "home.footer.agentPlatform": "Kwaipilot Agent Open Platform",
  "home.cards.codeCompletion.title": "Code Completion · Code at Will",
  "home.cards.codeCompletion.desc":
    "Real-time line/function-level completion, just press Tab to accept",
  "home.cards.naturalLanguage.title":
    "Natural Language Code Generation · Flow Uninterrupted",
  "home.cards.naturalLanguage.desc":
    "Generate code and comments directly in the editor through natural language descriptions",
  "home.cards.knowledgeQA.title":
    "Repository-level Code Knowledge Q&A · Everything Under Control",
  "home.cards.knowledgeQA.desc":
    "Enhanced with domain code and related data knowledge, easily solve development challenges through natural language conversations",
  // Logo related
  "logo.subtitle": "The AI tool that understands Kuaishou development best",
  "Code Smarter, Build Faster": "Code Smarter, Build Faster",

  // Navigation
  "nav.agent": "Agent",
  "nav.chat": "Chat",
  "nav.history": "History",
  "nav.developerMode": "Developer Mode",
  "nav.developerModeEnabled": "Developer mode enabled",
  "nav.developerModeDisabled": "Developer mode disabled",
  // Dialog related
  "dialog.unprocessedChanges": "Unprocessed Changes", // Added
  "dialog.unprocessedChangesMessage": "{{count}} files have unprocessed changes", // Added
  "dialog.responseGenerating": "Generating Response", // Added
  "dialog.switchTabWarning":
    "Switching tabs will stop current generation. Continue?", // Added
  "checkpoint.confirmRollback": "Confirm rollback?",
  "checkpoint.rollbackWarning":
    "All code changes will be rolled back to before this conversation occurred, and subsequent conversation records will be cleared. Do you want to continue?",

  // Notification related
  "notification.chatgptMerged": "ChatGPT Merged", // Added
  // Terminal related
  "terminal.commandLine": "Command Line", // Added
  "terminal.openTerminal": "Open Terminal", // Added
  "terminal.backgroundCommand": "Background Command", // Added
  // 是否运行
  "terminal.commandLinePlaceholder": "Continue?", // 是否运行

  "terminal.riskCommand":
    "Agent determined this command is risky and requires manual confirmation",
  "terminal.blacklistCommand":
    "This command prefix is in the blacklist and cannot be executed automatically",
  "terminal.blacklistPrefix": "This command prefix is in",
  "terminal.blockListButton": "blacklist setting, ",
  "terminal.blacklistSuffix": "cannot auto-execute",

  // Chat Status
  "chat.analyzing": "Analyzing",
  "chat.thinkingInProgress": "Thinking",
  "chat.emptyResponse": "Model returned empty content",
  "chat.contextCleared": "Context cleared",
  "chat.sessionInactive":
    "Session has been inactive for a long time, context cleared.",
  "chat.restoreContext": "Restore context",
  "chat.contextRestored": "Context memory restored",
  "chat.continuePrompt": "continue",

  // Error Messages
  "error.regenerateNoHistory":
    "No conversation history exists when regenerating, please contact oncall",
  "error.generatingInProgress": "Generating response, please try again later",
  "error.noLastMessageConfig":
    "Unable to get the configuration of the last message, please contact oncall",
  "error.serviceException": "Sorry, service exception, please contact oncall",
  "error.tooManyRequests":
    "Sorry, too many concurrent users, please try again later",
  "error.fileDeleted": "File has been deleted",
  "error.filePathNotFound": "File path not found",
  "error.terminalCommandFailed": "Terminal command execution failed",
  "error.unknownError": "Unknown error",
  "error.terminalCommandFailedCheckStatus":
    "Terminal command execution failed, please check terminal status",

  // Buttons and Actions
  "button.copy": "Copy",
  "button.copySuccess": "Copy successful",
  "button.insertToTerminal": "Insert to terminal",
  "button.insertToCursor": "Insert at cursor",
  "button.apply": "Apply",
  "button.reapply": "Reapply",
  "button.expand": "Expand",
  "button.collapse": "Collapse",
  "button.showCode": "Hide code",

  // File and Code Operations
  "file.changes": "Changes",
  "file.changesCount": "files",
  "file.lines": "Lines",
  "file.related": "Related references",
  "file.expandLines": "Expand {{count}} lines of code",
  "file.applyStatus.accepted": "Accepted",
  "file.applyStatus.applied": "Applied",
  "file.applyStatus.applying": "Applying...",
  "file.applyStatus.rejected": "Rejected",

  // Generation Status
  "generation.generating": "Generating...",
  "generation.applying": "Applying",
  "generation.interrupted": "Generation interrupted, please retry",
  "generation.multipleFiles": "Total",
  "generation.filesCount": "files",

  // Terminal Messages
  "terminal.multilineCommandInserted":
    "Multi-line command inserted to terminal, please execute as needed",
  "terminal.commandSent": "Command sent to terminal",

  // Repository Related
  "repo.name": "Repository",
  "repo.notLinked": "No repository linked",
  "repo.linked": "Linked {{repo}}",
  "repo.linkRepo": "Link repository",
  "repo.clearSelection": "Clear selection",

  // Artifact Related
  "artifact.untitled": "Untitled",

  // Thinking Process
  "think.process": "Thinking process",
  "think.duration": "s",

  // Prediction/Accept
  "prediction.accept": "Accept (Tab)",
  "prediction.cancel": "Cancel (Esc)",

  // User Messages
  "user.copyIdSuccess": "ID copied to clipboard {{id}}",

  // File Selection Prompt
  "file.selectCodeOrFile": "Please select a piece of code or a file",

  // Error Handling Related
  "error.parsePromptFailed":
    "Failed to parse prompt template, please try contacting the prompt owner, error message: {{error}}",
  "error.unsupportedSymbol": "Symbol is not currently supported",

  // Actions component related
  "actions.continueGeneration": "Continue",
  "actions.outputTooLong": "Output too long, continue to get more results",
  "actions.regenerate": "Regenerate",
  "actions.previous": "Previous",
  "actions.next": "Next",

  // ListFile component related
  "listFile.relatedResults": "{{count}} related results",
  "listFile.noResults": "No results",

  // GrepSearch component related
  "grepSearch.relatedResults": "{{count}} related results",
  "grepSearch.noResults": "No results",

  // AutoRunSelect component related
  "autoRunSelect.autoRun": "Auto Run",
  "autoRunSelect.askBeforeRun": "Ask Before Run",
  "autoRunSelect.switchToAutoRun": "Switch to Auto Run",
  "autoRunSelect.description": "When enabled, the agent will automatically execute commands except those in the blacklist",
  "autoRunSelect.mcpDescription": "and MCP",
  "autoRunSelect.securityWarning": ", please be aware of potential security risks",
  "autoRunSelect.mcpAutoRunDescription": "When enabled, the agent will automatically execute MCP and commands except those in the blacklist. Please be aware of potential security risks",
  "autoRunSelect.cancel": "Cancel",
  "autoRunSelect.confirm": "Confirm",

  // MentionNodeLabel component related
  "mentionNodeLabel.web": "Web",
  "mentionNodeLabel.codebase": "Codebase",
  "mentionNodeLabel.unknownType": "Unknown Type",

  // CodeShow component related
  "codeShow.expandLines": "Expand {{count}} lines of code",
  "codeShow.collapseCode": "Collapse code",
};
