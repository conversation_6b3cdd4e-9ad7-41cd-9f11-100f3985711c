export const zh = {
  // 通用
  "common.confirm": "确认",
  "common.cancel": "取消",
  "common.copy": "复制",
  "common.copied": "已复制",
  "common.loading": "加载中...",
  "common.error": "错误",
  "common.success": "成功",
  "common.noResults": "暂无结果",
  "common.searchNoResults": "搜索无结果",
  "common.continue": "继续",
  "common.accept": "接受",
  "common.reject": "拒绝",
  "common.run": "运行",
  "common.cancel_action": "取消",
  "common.parameters": "参数",
  "common.result": "结果",
  "common.file": "文件",
  "common.add": "添加",
  "common.view": "查看",
  "common.open": "打开",
  "common.management": "管理",
  "common.market": "市场",
  "common.beta": "Beta",

  // 错误和状态
  "error.applicationError": "应用出错了",
  "error.contactSupport": "请携带以下信息反馈给 kwaipilot 客服, 或",
  "error.refreshPage": "刷新页面",
  "error.loadingFailed": "加载失败",
  "error.imageLoadFailed": "图片加载失败",
  "error.fileEditFailed": "文件编辑失败",
  "error.toolCallFailed": "工具调用失败",
  "error.runFailed": "运行失败",
  "error.noCheckpoint": "没有找到相关的检查点",
  "error.noMessage": "没有找到相关的消息",
  "error.renderingError": "渲染错误",
  "error.editorRenderingError": "编辑器发生渲染错误， 请确认插件版本为最新",
  "error.rulesTimeout": "获取规则超时",
  "error.submitError": "提交过程错误",
  "error.contactOncall": "请联系 kwaipilot oncall",
  "error.getCurrentFileSelectionFailed": "获取当前文件选择失败",

  // 消息相关
  "message.detached": "以上消息未找到所归属的对话",
  "message.orphaned": "以上消息未找到所归属的对话，联系 oncall 解决",
  // 登录和账户
  "login.greeting":
    "Hi，我是Kwaipilot，最懂快手研发的Ai工具。立即登录账号，开启Ai编程旅途吧！",
  "login.loginAction": "登录Kwaipilot",
  "login.manual": "查看Kwaipilot使用手册",
  "login.pleaseLogin": "请先登录",
  "login.copySuccess": "复制成功",
  "login.sessionIdCopied": "sessionId已复制到剪贴板 {{id}}",
  "login.chatIdCopied": "chatId已复制到剪贴板 {{id}}",

  // 历史记录
  "history.currentWorkspace": "当前工作区",
  "history.otherWorkspace": "其他工作区",
  "history.allShown": "全部显示完了",
  "history.delete": "删除",
  "history.noHistory": "暂无历史记录",
  "history.clear": "清空历史会话",
  "history.edit": "编辑",
  "history.confirm": "确定",
  "history.current": "· 当前",

  // 聊天相关
  "chat.regenerate": "重新生成",
  "chat.previous": "上一个",
  "chat.next": "下一个",
  "chat.foundDocuments": "找到 {{count}} 个相关文档",
  "chat.generatingPleaseTryLater": "正在生成中，请稍后再试",
  // 聊天界面
  "chat.stopGeneration": "停止生成",
  "chat.thinking": "思考中",
  "chat.generating": "生成中",
  "chat.applying": "应用中",
  "chat.waitingForOperation": "等待操作",
  "chat.taskCompleted": "任务完成",
  "chat.executing": "执行中",
  "chat.codeGenerationStopped": "已暂停生成代码",
  "chat.composerModeHint": "当前处于助理模式(beta版), 如需AI对话 请创建",
  "chat.placeholder1": "有问题尽管问我，使用 # 引用知识",
  "chat.placeholder": "有问题尽管问我，使用 # 引用知识、/ 唤起指令",
  // 输入框和提及功能
  "mention.file": "文件",
  "mention.folder": "目录",
  "mention.knowledge": "知识库",
  "mention.rule": "规则",
  "mention.customPrompt": "自定义指令",
  "mention.addRule": "添加规则",
  "mention.web": "联网",
  "mention.codebase": "代码库",
  "mention.command": "指令",
  "mention.recent": "最近",
  "mention.currentFile": "当前文件",
  "mention.addProjectRule": "添加项目规则",
  "mention.clickOrReturn": "点击或",
  "mention.return": "返回",
  "mention.noMatch": "无匹配结果",
  "mention.noResults": "暂无结果",
  "mention.noCustomPrompts": "暂无自定义指令，前往",
  "mention.promptManagement": "提示词管理平台",
  "mention.create": "新建",
  "mention.noRules": "暂无规则",
  "mention.knowledgeOnlyOne": "当前仅支持单选",
  "mention.inputPlaceholder": "请输入",
  "mention.knowledgeEmptyText": "知识",
  "mention.searchNoResults": "搜索无结果",
  "mention.openAFolderFirst": "请先打开一个文件夹",
  // 斜杠命令
  "slashCommand.funcComment": "函数注释",
  "slashCommand.lineCodeComment": "行间注释",
  "slashCommand.codeExplain": "代码解释",
  "slashCommand.codeRefactor": "代码优化",
  "slashCommand.funcSplit": "函数拆分",
  "slashCommand.unitTest": "单元测试",
  "slashCommand.customPrompt": "自定义指令",
  "slashCommand.clearContext": "清除上下文",
  "slashCommand.command": "指令",

  // Composer页面
  "composer.deepMode": "深度模式",
  "composer.moreCapabilities": "更多能力详见",
  "composer.userManual": "Kwaipilot使用手册",
  "composer.modifyCurrentPrompt": "修改当前聊天Prompt",
  "composer.modifyCurrentPromptDesc": "修改当前聊天会话的prompt",
  "composer.openNewChatWithPrompt": "打开新聊天并设置Prompt",
  "composer.openNewChatWithPromptDesc": "创建新的聊天会话并设置初始prompt",
  "composer.newSession": "新会话",
  "composer.notCurrentWorkspace": "非当前工作区对话",
  "composer.notCurrentWorkspaceDesc":
    "该会话不属于当前工作区，为保障您的体验可以",
  "composer.createNewSession": "新建会话",
  "Code Smarter, Build Faster": "让代码更聪明，工作更高效",

  // MCP相关
  "mcp.status.available": "可使用",
  "mcp.status.preparing": "准备中",
  "mcp.status.unavailable": "不可使用",
  "mcp.configError": "MCP 配置文件异常",
  "mcp.noServers": "暂无 MCP Servers",
  "mcp.management": "MCP 管理",
  "mcp.market": "MCP 市场",
  "mcp.riskCommand": "该命令agent判断有风险，需手动确认",
  "mcp.blacklistCommand": "该命令前缀在黑名单设置内，无法自动执行",
  "mcp.blacklistPrefix": "该命令前缀在",
  "mcp.blacklistSuffix": "黑名单中，无法自动执行",

  // 代码和文件操作
  "code.copy": "复制",
  "code.accept": "接受",
  "code.reject": "拒绝",
  "code.reapply": "重新应用",
  "code.showCodeView": "展示代码视图",
  "code.showDiffView": "展示diff视图",
  "code.noChanges": "无变更建议",
  "code.generating": "正在生成",
  "code.applying": "正在应用",
  "code.pendingReview": "待审查",
  "code.totalChanges": "共变更",
  "code.filesChanged": "个文件",
  "code.rejected": "(已拒绝)",
  "code.rejectAll": "全部拒绝",
  "code.acceptAll": "全部接受",
  "code.indexNotComplete": "代码索引构建未完成",
  "code.indexIncompleteDesc":
    "当前索引构建未完成，这可能会影响后续回答输出的效果，点击查看构建进度",
  // 工具操作
  "tool.readTask": "读取任务",
  "tool.searchMemory": "搜索记忆",
  "tool.saveMemory": "保存记忆",
  "tool.fileView": "文件查看",
  "tool.codeSearch": "代码搜索",
  "tool.fileList": "文件列表查询",
  "tool.regexSearch": "正则搜索",
  "tool.writeFile": "写入文件",
  "tool.commandExecution": "命令行执行",
  "tool.codebaseSearch": "代码库搜索",
  "tool.resultsFound": "个相关结果",

  // 自动运行设置
  "autorun.mode": "自动运行",
  "autorun.askBeforeRun": "运行前询问",
  "autorun.switchToAutoRun": "切换为自动运行",
  "autorun.description": "开启后，智能体将自动执行除黑名单外的命令",
  "autorun.mcpDescription": "和 mcp",
  "autorun.securityWarning": "，请注意可能的潜在安全风险",
  "autorun.mcpAutoRun":
    "开启后，智能体将自动执行mcp和除黑名单外的命令，请注意可能的潜在安全风险",

  // 对话和消息
  "conversation.confirmSend": "确定要发送此消息吗？",
  "conversation.historyWillBeCleared": "后续的对话记录将会被清除，是否继续？",
  "conversation.confirmSendTitle": "确认发送？",
  "conversation.restoreAndSend":
    "所有代码变更都将被回退到该对话发生之前，且后续的对话记录将会被清除，是否继续？",
  "conversation.onlyModifyConversation": "仅修改对话",
  "conversation.continueAndRestore": "继续且回退代码",
  "conversation.restoreToCurrentAnswer": "恢复到当前回答？",
  "conversation.restoreWarning": "所有代码变更将被恢复到此版本之前。是否继续？",
  "conversation.noCheckpointSupport": "该对话不支持回滚",
  "conversation.vscodeWorkspaceNotSupported": "VSCode工作区模式不支持对话回滚",
  "conversation.restoreToConversationStart": "回滚至此对话开始前",
  "conversation.confirmRollback": "确认回滚？",
  "conversation.rollbackWarning":
    "所有代码变更都将被回退到该对话发生之前，且后续的对话记录将会被清除，是否继续？",

  // 对话框相关
  "dialog.unprocessedChanges": "有未处理的变更",
  "dialog.unprocessedChangesMessage": "有 {{count}} 个文件存在未处理的变更",
  "dialog.responseGenerating": "正在生成回复",
  "dialog.switchTabWarning": "切换标签页将停止当前生成，是否继续？",
  "checkpoint.confirmRollback": "确认回退？",
  "checkpoint.rollbackWarning":
    "所有代码变更都将被回退到该对话发生之前，且后续的对话记录将会被清除，是否继续？",

  // 通知相关
  "notification.chatgptMerged": "ChatGPT 已合并",

  // 终端相关
  "terminal.commandLine": "命令行",
  "terminal.openTerminal": "打开终端",
  "terminal.backgroundCommand": "后台命令",
  "terminal.commandLinePlaceholder": "是否运行", // Added

  "terminal.riskCommand": "该命令agent判断有风险，需手动确认",
  "terminal.blacklistCommand": "该命令前缀在黑名单设置内，无法自动执行",
  "terminal.blacklistPrefix": "该命令前缀在",
  "terminal.blockListButton": "黑名单设置",
  "terminal.blacklistSuffix": "内，无法自动执行",

  // 诊断和修复
  "diagnostic.codeErrors": "处代码错误",
  "diagnostic.autoFixSettings": "自动修复设置",
  "diagnostic.fix": "修复",
  "diagnostic.fixErrors": "修复这些报错",
  "diagnostic.codeErrorsCount": "{{count}}处代码错误",
  // 其他功能
  "inlineChat.naturalLanguageCode": "自然语言生成代码",
  "localService.connectionLost":
    "本地服务连接已丢失，请检查本地服务是否正常运行",
  "upload.addImage": "添加图片",
  "upload.modelNotSupportImage": "当前模型 {{modelName}} 不支持图片上传",
  "upload.supportedFiles":
    "支持上传多个文件（总大小不超过 3MB），支持 .pdf、.docx、.txt 代码文件 等",
  "upload.fileInfo": "上传的文件信息",
  "upload.UploadFileBtn": "上传文件",
  "upload.useChatSubmit": "使用聊天提交",
  "artifact.previewTrigger": "触发预览",
  "artifact.thinkInfoCalculation": "计算 thinkInfo",
  "artifact.stillThinking": "还在思考中, 则更新 cost",
  "artifact.compatibilityNote": "兼容没有 answerId 的历史数据",
  "like.like": "喜欢",
  "like.dislike": "不喜欢",
  "parsing.parsing": "解析",
  "parsing.filePathFailed": "获取文件路径失败",
  "state.statusUpdate": "状态更新",
  "tooltip.copy": "复制",
  "tooltip.reject": "拒绝",
  "tooltip.accept": "接受",
  "tooltip.reapply": "重新应用",

  // 首页相关
  "home.footer.help": "帮助文档",
  "home.footer.bestPractice": "最佳实践",
  "home.footer.joinGroup": "加入用户群",
  "home.footer.moreProducts": "更多产品",
  "home.footer.officialWebsite": "Kwaipilot 官网",
  "home.footer.chatEngine": "Kwaipilot 智能问答引擎",
  "home.footer.agentPlatform": "Kwaipilot 智能体开放平台",
  "home.cards.codeCompletion.title": "代码续写 · 码随心动",
  "home.cards.codeCompletion.desc":
    "行级/函数级实时续写，只需单击 Tab 键即可采纳",
  "home.cards.naturalLanguage.title": "自然语言生成代码 · 心流不止",
  "home.cards.naturalLanguage.desc":
    "通过自然语言描述问题或需求，在编辑器内直接生成代码及注释",
  "home.cards.knowledgeQA.title": "仓库级代码知识问答 · 尽在掌握",
  "home.cards.knowledgeQA.desc":
    "基于域内代码及关联数据知识增强，自然语言对话轻松解决研发难题",

  // Logo相关
  "logo.subtitle": "最懂快手研发的 AI 工具",
  // 导航栏
  "nav.agent": "智能体",
  "nav.chat": "问答",
  "nav.history": "历史",
  "nav.developerMode": "开发者模式",
  "nav.developerModeEnabled": "开发者模式已开启",
  "nav.developerModeDisabled": "开发者模式已关闭",

  // 对话状态
  "chat.analyzing": "分析中",
  "chat.thinkingInProgress": "思考中",
  "chat.emptyResponse": "模型返回内容为空",
  "chat.contextCleared": "上下文已清空",
  "chat.sessionInactive": "会话长时间未活跃，上下文已清空。",
  "chat.restoreContext": "恢复上下文",
  "chat.contextRestored": "已恢复上下文记忆",
  "chat.continuePrompt": "继续",

  // 错误消息
  "error.regenerateNoHistory": "重新生成时不存在对话历史，请联系oncall查看",
  "error.generatingInProgress": "正在生成回答，请稍后尝试",
  "error.noLastMessageConfig": "无法获取最后一条消息的配置，请联系oncall查看",
  "error.serviceException": "抱歉，服务异常，请联系 oncall 解决",
  "error.tooManyRequests": "抱歉，当前访问人数较多，请稍后重试",
  "error.fileDeleted": "文件已被删除",
  "error.filePathNotFound": "文件路径未找到",
  "error.terminalCommandFailed": "执行终端命令失败",
  "error.unknownError": "未知错误",
  "error.terminalCommandFailedCheckStatus": "执行终端命令失败，请检查终端状态",

  // 按钮和操作
  "button.copy": "复制",
  "button.copySuccess": "复制成功",
  "button.insertToTerminal": "插入至终端中",
  "button.insertToCursor": "插入至光标处",
  "button.apply": "应用",
  "button.reapply": "重新应用",
  "button.expand": "展开",
  "button.collapse": "收起",
  "button.showCode": "收起代码",

  // 文件和代码操作
  "file.changes": "变更",
  "file.changesCount": "个文件",
  "file.lines": "行",
  "file.related": "相关引用",
  "file.expandLines": "展开 {{count}} 行代码",
  "file.applyStatus.accepted": "已接受",
  "file.applyStatus.applied": "已应用",
  "file.applyStatus.applying": "应用中...",
  "file.applyStatus.rejected": "已拒绝",

  // 生成状态
  "generation.generating": "正在生成...",
  "generation.applying": "正在应用",
  "generation.interrupted": "生成中断，请重试",
  "generation.multipleFiles": "共",
  "generation.filesCount": "个文件",

  // 终端消息
  "terminal.multilineCommandInserted": "多行命令已插入终端，请按需执行",
  "terminal.commandSent": "命令已发送到终端",

  // 仓库相关
  "repo.name": "仓库",
  "repo.notLinked": "未关联仓库",
  "repo.linked": "已关联{{repo}}",
  "repo.linkRepo": "关联仓库",
  "repo.clearSelection": "清除已选项",

  // Artifact 相关
  "artifact.untitled": "未命名",

  // 思考过程
  "think.process": "思考过程",
  "think.duration": "s",

  // 预测/采纳
  "prediction.accept": "采纳 (Tab)",
  "prediction.cancel": "取消 (Esc)",

  // 用户消息
  "user.copyIdSuccess": "Id已复制到剪贴板 {{id}}",

  // 文件选择提示
  "file.selectCodeOrFile": "请选择一段代码或一个文件",

  // 错误处理相关
  "error.parsePromptFailed":
    "解析 prompt 模板失败，请尝试联系prompt负责人，错误信息:{{error}}",
  "error.unsupportedSymbol": "暂不支持 Symbol",

  // Actions 组件相关
  "actions.continueGeneration": "继续生成",
  "actions.outputTooLong": "输出过长，继续获取更多结果",
  "actions.regenerate": "重新生成",
  "actions.previous": "上一条",
  "actions.next": "下一条",

  // ListFile 组件相关
  "listFile.relatedResults": "{{count}}个相关结果",
  "listFile.noResults": "暂无结果",

  // GrepSearch 组件相关
  "grepSearch.relatedResults": "{{count}}个相关结果",
  "grepSearch.noResults": "暂无结果",

  // AutoRunSelect 组件相关
  "autoRunSelect.autoRun": "自动运行",
  "autoRunSelect.askBeforeRun": "运行前询问",
  "autoRunSelect.switchToAutoRun": "切换为自动运行",
  "autoRunSelect.description": "开启后，智能体将自动执行除黑名单外的命令",
  "autoRunSelect.mcpDescription": "和 mcp",
  "autoRunSelect.securityWarning": "，请注意可能的潜在安全风险",
  "autoRunSelect.mcpAutoRunDescription": "开启后，智能体将自动执行mcp和除黑名单外的命令，请注意可能的潜在安全风险",
  "autoRunSelect.cancel": "取消",
  "autoRunSelect.confirm": "确认",

  // MentionNodeLabel 组件相关
  "mentionNodeLabel.web": "联网",
  "mentionNodeLabel.codebase": "代码库",
  "mentionNodeLabel.unknownType": "未知类型",

  // CodeShow 组件相关
  "codeShow.expandLines": "展开 {{count}} 行代码",
  "codeShow.collapseCode": "收起代码",
};
