import * as ReactDOM from "react-dom/client";
import { ChakraProvider } from "@chakra-ui/react";
import AppWrapper from "@/AppWrapper"; // Updated from App to AppWrapper
import theme from "@/utils/theme";
import CodeImage from "@/components/Prediction/codeImage";
import { initHighlighterInstance } from "./utils/highlighter";
import { ConfigProvider as AntConfigProvider } from "antd";
import { IdeEnvProvider } from "./providers/IdeEnvProvider";
import { getRootContainer } from "./utils/dom";
import i18n from "i18next";
import { config as I18nConfig } from "./i18n";
import ErrorBoundary from "@/components/ErrorBoundary";
import DefaultFallback from "@/components/ErrorBoundary/DefaultFallback";

i18n.init({
  ...I18nConfig,
  lng: "zh",
  fallbackLng: "zh", // 设置回退语言为中文
}).then(() => {
  initHighlighterInstance().then(() => {
    const rootElement = getRootContainer();

    ReactDOM.createRoot(rootElement).render(
      <ErrorBoundary fallback={(error, resetError) => <DefaultFallback error={error} resetError={resetError} />}>
        <AntConfigProvider
          theme={{
            token: {
              fontFamily: "var(--vscode-font-family)",
            },
          }}
        >
          <ChakraProvider theme={theme}>
            <IdeEnvProvider value={{ isKwaiPilotIDE: false }}>
              <CodeImage />
              <AppWrapper />
            </IdeEnvProvider>
          </ChakraProvider>
        </AntConfigProvider>
      </ErrorBoundary>,
    );
  });
});
