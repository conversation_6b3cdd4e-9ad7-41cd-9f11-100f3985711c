import { kwaiPilotBridgeAPI } from "@/bridge";
import { ext2LanguageId } from "@/constant";
import { httpClient } from "@/http";
import {
  CodeSearchCheckoutRequest,
  GeneratePromptFile,
} from "@/http/interface";
import { getUserInfo } from "@/utils/getUserInfo";
import { logger } from "@/utils/logger";

const maxLen = 20;

/**
 * TODO: 和 vscode 插件部分的 enum 公用
 */
export enum WorkspaceState {
  /** 选择过的文件 */
  CODE_SEARCH_SELECT_FILE_HISTORY = "codeSearchSelectFileHistory",
  /** 选择过的目录目录 */
  CODE_SEARCH_SELECT_DIR_HISTORY = "codeSearchSelectDirHistory",
  /** 打开的文件 and 打开过的文件 */
  CODE_SEARCH_SELECT_OPEN_FILE_HISTORY = "codeSearchSelectOpenFileHistory",
  /** 打开文件的目录 and 打开过文件的目录 */
  CODE_SEARCH_SELECT_OPEN_DIR_HISTORY = "codeSearchSelectOpenDirHistory",
  /** 当前工作区激活的会话 */
  ACTIVE_SESSION_ID = "activeSessionId",
  ACTIVE_COMPOSER_SESSION_ID = "activeComposerSessionId",
}

type CodeSearchSelectKey =
  | WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY;

class RepoChatService {
  private loggerScope = "repo-chat";
  private codeSearchController: AbortController | null;
  private remoteOriginUrl?: string;
  private workspacePath?: string;
  private isGitRepo = false;
  private activeFilePath?: string;
  private initPromise: Promise<void>;

  constructor() {
    this.codeSearchController = null;
    this.initPromise = this.initialize();
  }

  private async initialize() {
    await Promise.all([
      this.setCodeSearchSelectOpenFileAndFoldHistory(),
      kwaiPilotBridgeAPI.getActiveEditor().then((editor) => {
        this.currentFilePath = editor.document.relativePath;
      }),
      this.initCodeSearch(),
    ]);
  }

  async setCodeSearchSelectOpenFileAndFoldHistory() {
    const { list: files } = await kwaiPilotBridgeAPI.getOpenTabFiles();
    if (!files) {
      return;
    }
    logger.info("update open file and dir", this.loggerScope);

    this.setSelectFileQueue(
      WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY,
      files,
    );
    this.setSelectFileQueue(
      WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY,
      files.map(this.handleFilePathToDir),
    );
  }

  /** 开始构建索引 */
  async startIndexBuild(params: CodeSearchCheckoutRequest) {
    if (this.codeSearchController) {
      this.codeSearchController.abort();
    }

    const newController = new AbortController();
    this.codeSearchController = newController;
    try {
      await this.indexBuild(params, newController.signal);
    }
    catch (error) {
      logger.error("index build error", this.loggerScope, {
        reason: "index build error",
        err: error,
      });
    }
  }

  /** 初始化构建索引 */
  async initCodeSearch() {
    const userInfo = await getUserInfo();

    if (userInfo) {
      // NOTE 需要一个 bridge 获取当前所在目录
      const repoPath = await this.getRepoPath();
      const { result: inRepo } = (await kwaiPilotBridgeAPI.executeCmd(
        `git -C ${repoPath} rev-parse --is-inside-work-tree`,
      )) as { result: string };

      if (inRepo === "true") {
        this.isGitRepo = true;
        const { result: remoteOriginUrl }
          = (await kwaiPilotBridgeAPI.executeCmd(
            `git -C ${repoPath} config --get remote.origin.url`,
          )) as { result: string };
        this.remoteOriginUrl = remoteOriginUrl;
        const isKwaiGitRepo
          = remoteOriginUrl?.startsWith("*************************")
          || remoteOriginUrl?.startsWith("https://git.corp.kuaishou.com")
          || false;

        if (isKwaiGitRepo) {
          this.startIndexBuild({
            repoName: this.handleGitUrl(remoteOriginUrl),
            branch: await this.getBranch(),
            commit: await this.getCommit(),
            username: userInfo.name,
          });
        }
        else {
          logger.info("init code search error", this.loggerScope, {
            reason: "repo is not kwai repo",
          });
        }
      }
      else {
        logger.info("init code search error", this.loggerScope, {
          reason: "repoPath is null",
        });
      }
    }
    else {
      logger.info("init code search error", this.loggerScope, {
        reason: "userInfo is null",
      });
    }
  }

  async indexBuild(params: CodeSearchCheckoutRequest, signal: AbortSignal) {
    let buildStatus = (await httpClient.getCodeSearchCheckout(params, signal))
      .status;

    if (buildStatus === -1) {
      logger.info("index build", this.loggerScope, {
        reason: "this repo not support code search",
        value: params.repoName,
      });
      return;
    }

    buildStatus = (await httpClient.getCodeSearchCheckout(params, signal))
      .status;
  }

  /** 获取指令选中的文件内容 */
  async getSelectFileCode(absolutePaths: string[]) {
    const res: GeneratePromptFile[] = [];
    for (const f of absolutePaths) {
      try {
        const { content } = await kwaiPilotBridgeAPI.fs.readFile(f);
        res.push({
          code: content,
          language: ext2LanguageId[f.split(".").pop() ?? ""] ?? "",
          name: f,
        });
      }
      catch (err: any) {
        logger.error("get select file code error", this.loggerScope, {
          reason: "get select file code error",
          err: err,
        });
      }
    }
    return res;
  }

  async getCurrentFilePathAndRepoPath() {
    await this.initPromise;
    const repoPath = await this.getRepoPath();

    return {
      filePath: this.activeFilePath,
      repoPath,
    };
  }

  async getWorkspacePathAndRepoPath() {
    return {
      workspacePath: this.workspacePath,
      repoPath: await this.getRepoPath(),
    };
  }

  async setSelectFileQueue(key: CodeSearchSelectKey, path: string[]) {
    const queue
      = (await kwaiPilotBridgeAPI.getState<string[]>(key)).value ?? [];
    const workspacePath = await this.getRepoPath();

    const relativePaths = path.map(p =>
      p.startsWith(workspacePath) ? p.slice(workspacePath.length) : p,
    );
    const newQueue = Array.from(new Set([...relativePaths, ...queue]));

    kwaiPilotBridgeAPI.updateState(key, newQueue.slice(0, maxLen));
  }

  /** 更新代码搜索选择的历史 */
  updateCodeSearchSelectHistory(data: { file: string[]; dir: string[] }) {
    const { file, dir } = data;
    if (file.length) {
      this.setSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY,
        file,
      );
    }

    if (dir.length) {
      this.setSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY,
        dir,
      );
    }
  }

  /** 处理 git url */
  private handleGitUrl(gitUrl: string) {
    const prefix = gitUrl.startsWith("https://")
      ? "https://git.corp.kuaishou.com/"
      : "*************************:";
    const suffix = ".git";
    let str = gitUrl.startsWith(prefix) ? gitUrl.slice(prefix.length) : gitUrl;
    str = str.endsWith(suffix) ? str.slice(0, -suffix.length) : str;
    return str;
  }

  public handleFilePathToDir(filePath: string) {
    return filePath.split("/").slice(0, -1).join("/");
  }

  get repoName() {
    return this.handleGitUrl(this.remoteOriginUrl ?? "");
  }

  get remoteUrl() {
    return this.remoteOriginUrl;
  }

  get currentFilePath() {
    return this.activeFilePath || "";
  }

  set currentFilePath(filePath: string) {
    this.activeFilePath = filePath;
  }

  async getCommit() {
    const repoPath = await this.getRepoPath();
    const { result: commit } = (await kwaiPilotBridgeAPI.executeCmd(
      `git -C ${repoPath} rev-parse HEAD`,
    )) as { result: string };
    return commit;
  }

  async getBranch() {
    try {
      const repoPath = await this.getRepoPath();
      const { result: branch } = (await kwaiPilotBridgeAPI.executeCmd(
        `git -C ${repoPath} rev-parse --abbrev-ref HEAD`,
      )) as { result: string };

      return branch;
    }
    catch (e) {
      return "";
    }
  }

  async getRepoPath() {
    const { result: workspaceUri }
      = (await kwaiPilotBridgeAPI.getWorkspaceUri()) as {
        result: string;
      };
    if (!workspaceUri) {
      this.workspacePath = "";
      return "";
    }
    if (workspaceUri.startsWith("file://")) {
      this.workspacePath = workspaceUri.slice(7);
    }
    else {
      this.workspacePath = workspaceUri;
    }
    return this.workspacePath;
  }

  getAbsolutePath(filePath: string) {
    if (!this.workspacePath) {
      logger.error("workspacePath is null", this.loggerScope);
      return filePath;
    }
    const repoPath = this.workspacePath;
    const repoPahtWithSlash = repoPath.endsWith("/")
      ? repoPath
      : repoPath + "/";
    const filePathWithNoSlash = filePath.startsWith("/")
      ? filePath.slice(1)
      : filePath;

    return repoPahtWithSlash + filePathWithNoSlash;
  }
}
const repoChatService = new RepoChatService();
export default repoChatService;
