import { kwaiPilotBridgeAPI } from "@/bridge";
import { VsContextStaticState, VsWorkspaceStaticState } from "shared/lib/bridge/protocol";

/**
 * 一些 vscode 的状态，在整个工作周期都不会变化，不需要通过 bridge 频繁获取，所以放在这里
 * 比如：
 * * vscode.workspace.workspaceFolders
 * * context.storageUri
 */
export class WorkspaceStaticState {
  private static instance: WorkspaceStaticState;

  private constructor() {
    // 私有构造函数，防止外部直接实例化
    this.initiation = new Promise(() => {
      kwaiPilotBridgeAPI.extensionMisc.$getWorkspaceStaticState().then(({ workspace, context }) => {
        this.workspace = workspace;
        this.context = context as any;
      });
    });
  }

  /**
   * workspace 如果调用较早，最好 await workspaceState.initiation 后再使用
   */
  workspace!: VsWorkspaceStaticState;
  context!: VsContextStaticState;

  initiation!: Promise<void>;

  public static getInstance(): WorkspaceStaticState {
    if (!WorkspaceStaticState.instance) {
      WorkspaceStaticState.instance = new WorkspaceStaticState();
    }
    return WorkspaceStaticState.instance;
  }
}

// 为了方便访问，导出一个全局实例
export const workspaceStaticState = WorkspaceStaticState.getInstance();
