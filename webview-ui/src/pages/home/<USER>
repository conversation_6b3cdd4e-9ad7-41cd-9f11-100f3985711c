import React from "react";
import "@/pages/home/<USER>";
import { useColorMode } from "@chakra-ui/react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { t } from "i18next";

interface MenuItemProps {
  icon: React.ReactNode;
  text: string;
  link?: string;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, text, link }) => {
  const handleClick = () => {
    if (link) {
      kwaiPilotBridgeAPI.openUrl(link);
    }
  };
  return (
    <div
      className="flex items-center mr-[16px] opacity-80 hover:opacity-100 cursor-pointer"
      onClick={handleClick}
    >
      <div className="mr-[4px]">{icon}</div>
      <span className="footer-text inline-block truncate">{text}</span>
    </div>
  );
};

export default function HomeFooter() {
  const { colorMode: theme } = useColorMode();
  const bgStyle
    = theme === "light"
      ? {
          background: "white",
          border: "1px solid #E1EAF5",
          boxShadow:
            "0px 4px 16px 0px rgba(24, 68, 117, 0.08), 0px 1px 3px 0px rgba(24, 68, 117, 0.06)",
        }
      : {
          background: "#1b232e",
        };
  const handleClick = (link?: string) => {
    if (link) {
      kwaiPilotBridgeAPI.openUrl(link);
    }
  };
  return (
    <div className="text-[13px] flex px-[16px] pb-[16px] justify-between items-end w-full h-[40px] min-h-[40px] relative">
      <div className="flex justify-start item-center footer-menu">
        <MenuItem
          icon={
            <div className="bg-text-common-primary size-[14px] footer-help"></div>
          }
          link="https://docs.corp.kuaishou.com/k/home/<USER>"
          text={t("home.footer.help")}
        />
        <MenuItem
          icon={
            <div className="bg-text-common-primary size-[14px] footer-thumb"></div>
          }
          link="https://docs.corp.kuaishou.com/k/home/<USER>/fcADD3RVxoProAYoJ9e9jQ70U"
          text={t("home.footer.bestPractice")}
        />
        <MenuItem
          icon={
            <div className="bg-text-common-primary size-[14px] footer-kim"></div>
          }
          link="kim://thread?id=3705558479509962:4"
          text={t("home.footer.joinGroup")}
        />
      </div>
      <div className="flex justify-end relative group">
        <div className="flex justify-end items-center opacity-80 hover:opacity-100 cursor-pointer">
          <div className="bg-text-common-primary size-[16px] footer-more min-w-[16px]"></div>
          <div className="footer-more-text inline-block truncate  w-full">
            {t("home.footer.moreProducts")}
          </div>
        </div>
        <div className="absolute z-[100] right-0 bottom-[100%] hidden group-hover:flex">
          <div
            className="flex flex-col gap-[4px] rounded-[4px] p-[4px] mb-[4px]"
            style={bgStyle}
          >
            <div
              className="hover:bg-bg-hover rounded-[4px] w-[200px] py-[8px] px-[12px] flex items-center cursor-pointer"
              onClick={() => handleClick("https://kwaipilot.corp.kuaishou.com")}
            >
              <div className="bg-text-common-secondary size-[16px] footer-home-link mr-[4px]"></div>
              <div>{t("home.footer.officialWebsite")}</div>
            </div>
            <div
              className="hover:bg-bg-hover rounded-[4px] w-[200px] py-[8px] px-[12px] flex items-center cursor-pointer"
              onClick={() =>
                handleClick("https://kwaipilot.corp.kuaishou.com/chat")}
            >
              <div className="bg-text-common-secondary size-[16px] footer-chat mr-[4px]"></div>
              <div>{t("home.footer.chatEngine")}</div>
            </div>
            <div
              className="hover:bg-bg-hover rounded-[4px] w-[200px] py-[8px] px-[12px] flex items-center cursor-pointer"
              onClick={() =>
                handleClick("https://kwaipilot.corp.kuaishou.com/agents")}
            >
              <div className="bg-text-common-secondary size-[16px] footer-agents mr-[4px]"></div>
              <div>{t("home.footer.agentPlatform")}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
