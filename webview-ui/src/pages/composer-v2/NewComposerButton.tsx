import { SingleIcon } from "@/components/SingleIcon";
import { Icon } from "@/components/Union/t-iconify";
import { t } from "i18next";

export const NewComposerButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <SingleIcon
      onClick={onClick}
      className="size-[20px]"
      title={t("composer.newSession")}
    >
      <Icon icon="mynaui:chat-plus" />
    </SingleIcon>

  );
};
