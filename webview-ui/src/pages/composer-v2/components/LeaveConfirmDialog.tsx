import { AlertDialog } from "@/components/Union/chakra-ui";
import {
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogCloseButton,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Flex,
  AlertDialogProps,
} from "@chakra-ui/react";

import { useRef } from "react";
import IconAlert from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_warn_surface";
import KidIcon from "@/components/Union/kid";

import { DialogButton } from "@/logics/composer/components/DialogButton";
import { t } from "i18next";

export interface LeaveConfirmDialog_DirtyWorkingSetProps
  extends Omit<AlertDialogProps, "children" | "leastDestructiveRef"> {
  onCancel?: () => void;
  onContinue?: (action: "accept" | "reject") => void;
  indeterminatedWorkingSetFileNum: number;
}

export function LeaveConfirmDialog_DirtyWorkingSet(
  props: LeaveConfirmDialog_DirtyWorkingSetProps,
) {
  const {
    onCancel,
    onContinue,
    indeterminatedWorkingSetFileNum,
    ...restProps
  } = props;
  const cancelRef = useRef<HTMLButtonElement>(null);
  return (
    <AlertDialog {...restProps} leastDestructiveRef={cancelRef}>
      <AlertDialogOverlay>
        <AlertDialogContent className="!bg-editor-background">
          <AlertDialogHeader px={4} py={3} fontSize="13px" fontWeight="bold">
            <Flex alignItems="center" gap={1}>
              <KidIcon size={16} color="var(--vscode-editorWarning-background)" config={IconAlert} />
              {t("dialog.unprocessedChanges")}
            </Flex>
            <AlertDialogCloseButton />
          </AlertDialogHeader>

          <AlertDialogBody px={4} pt={0} pb={3}>
            {t("dialog.unprocessedChangesMessage", { count: indeterminatedWorkingSetFileNum })}
          </AlertDialogBody>

          <AlertDialogFooter px={4} gap={2} pt={0}>
            <DialogButton ref={cancelRef} onClick={() => onCancel?.()}>
              {t("common.cancel")}
            </DialogButton>
            <DialogButton isPrimary onClick={() => onContinue?.("reject")}>
              {t("code.rejectAll")}
            </DialogButton>
            <DialogButton isPrimary onClick={() => onContinue?.("accept")}>
              {t("code.acceptAll")}
            </DialogButton>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
}

export interface LeaveConfirmDialog_StreamingProps
  extends Omit<AlertDialogProps, "children" | "leastDestructiveRef"> {
  onCancel?: () => void;
  onContinue?: () => void;
}

export function LeaveConfirmDialog_Streaming(
  props: LeaveConfirmDialog_StreamingProps,
) {
  const { onCancel, onContinue, ...restProps } = props;
  const cancelRef = useRef<HTMLButtonElement>(null);
  return (
    <AlertDialog
      {...restProps}
      leastDestructiveRef={cancelRef}
    >
      <AlertDialogOverlay>
        <AlertDialogContent className="!bg-editor-background">
          <AlertDialogHeader px={4} py={3} fontSize="13px" fontWeight="bold">
            <Flex alignItems="center" gap={1}>
              <KidIcon size={16} color="#FFBB26" config={IconAlert} />
              {t("dialog.responseGenerating")}
            </Flex>
            <AlertDialogCloseButton />
          </AlertDialogHeader>

          <AlertDialogBody px={4} pt={0} pb={3}>
            {t("dialog.switchTabWarning")}
          </AlertDialogBody>

          <AlertDialogFooter px={4} gap={2} pt={0}>
            <DialogButton ref={cancelRef} onClick={() => onCancel?.()}>
              {t("common.cancel")}
            </DialogButton>
            <DialogButton isPrimary onClick={() => onContinue?.()}>
              {t("common.continue")}
            </DialogButton>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
}
