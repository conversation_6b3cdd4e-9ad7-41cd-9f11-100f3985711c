import { DEFAULT_MODEL_TYPE } from "@/constant";
import { getRecordStoreByVendor } from "@/store/record";
import { collectClick } from "@/utils/weblogger";
import { t } from "i18next";
import { useCallback, useEffect, useMemo, useState } from "react";
import Record from "./Record";

import LeftArrowIcon from "@/assets/left-arrow.svg?react";
import NoHistoryIcon from "@/assets/no-history.svg?react";
import { BriefSessionItem } from "@shared/types/chatHistory";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useNavigate } from "react-router-dom";
import { useHistoryStore } from "@/store/history";
import { setSessionToLocal } from "@/utils/sessionUtils";
import { setLocalStorageValue } from "@/utils/localStorage";
import { WorkspaceState } from "@/services/repo-chat";
import { PersistedComposerHistoryItem } from "shared/lib/agent/storage";
import { useAsync } from "react-use";
import { getActiveSessionIdFromStorage } from "../composer-v2/activeSessionStorage";

const recordStoreChatVendor = getRecordStoreByVendor("chat");
const recordStoreComposerVendor = getRecordStoreByVendor("composer");

const HistoryContent = () => {
  const historyList = useHistoryStore(state => state.historyList);
  const setHistoryList = useHistoryStore(state => state.setHistoryList);
  const navigate = useNavigate();
  const getWorkspaceUriFn = () => kwaiPilotBridgeAPI.getWorkspaceStorageUri().then(res => res.result || "");
  const { value: workspaceUri, loading: workspaceUriLoading } = useAsync(getWorkspaceUriFn, []);
  const noHistory = useMemo(() => !historyList.length, [historyList]);

  const [editingSession, setEditSession] = useState<BriefSessionItem | null>(
    null,
  );

  const selectComposerV2History = useCallback((record: BriefSessionItem) => {
    const query = new URLSearchParams();
    query.set("sessionId", record.sessionId);
    navigate({ pathname: "/composer-v2", search: `?${query.toString()}` });
  }, [navigate]);

  const getOldSessionList = useCallback(async () => {
    const list = [];
    let tempList: BriefSessionItem[] = [];
    let page = 1;
    do {
      const { sessionList } = await kwaiPilotBridgeAPI.getSession({
        page,
        pageSize: 50,
        timeRange: "all",
      });
      tempList = sessionList;
      page++;
      if (sessionList.length) {
        list.push(...sessionList);
      }
    } while (tempList.length);
    return list.map(i => ({
      ...i,
      sessionTime: String(new Date(i.sessionTime).getTime()),
    }));
  }, []);

  const getComposerSessionList = useCallback(async () => {
    const list = await kwaiPilotBridgeAPI.getComposerHistory();

    return list.map((item: PersistedComposerHistoryItem) => {
      return {
        sessionId: item.sessionId,
        sessionName: item.name,
        sessionTime: String(item.lastUpdatedAt),
        isComposer: true,
        workspaceUri: item.workspaceUri,
        isComposerV2: true,
      };
    });
  }
  , []);

  const orderHistoryList = useCallback((session1: BriefSessionItem[], session2: BriefSessionItem[]) => {
    // 创建新数组，不修改原数组
    const array = [...session1, ...session2];
    // 创建新数组进行排序
    const newArray = [...array].sort((a, b) => {
      // 从新到旧排序，用 b - a
      const timeA = parseInt(a.sessionTime);
      const timeB = parseInt(b.sessionTime);
      return timeB - timeA;
    });

    return newArray;
  }, []);
  const fetchHistoryList = useCallback(async () => {
    const [list1, list] = await Promise.all([
      getOldSessionList(),
      getComposerSessionList(),
    ]);

    const mergeList = orderHistoryList(list1, list);
    setHistoryList(mergeList);
  }, [getComposerSessionList, getOldSessionList, orderHistoryList, setHistoryList]);

  useEffect(() => {
    fetchHistoryList();
  }, [fetchHistoryList]);

  /** 选择某个历史记录 */
  const selectHistory = useCallback(
    (record: BriefSessionItem) => {
      collectClick("VS_CREATE_OR_CHANGE_CHAT");

      if (record.isComposerV2) {
        return selectComposerV2History(record);
      }
      const recordState = record.isComposer
        ? recordStoreComposerVendor.getState()
        : recordStoreChatVendor.getState();

      if (
        recordState.loadingStatu
        && record.sessionId !== recordState.activeSession
      ) {
        recordState.finishLoading();
        recordState.abortCurrentChat();
        if (recordState.sessionHistory) {
          const id = recordState.loadingStatu.id;
          const currentQA = recordState.sessionHistory.cachedMessages.find(
            item => item.id === id,
          );
          if (currentQA) {
            kwaiPilotBridgeAPI.addMessage({
              item: currentQA,
              sessionId: recordState.activeSession,
              chatId: id,
            });
          }
        }
      }

      recordState.setChatModelType(DEFAULT_MODEL_TYPE);

      recordState.updateActiveSessionAndSessionHistory(record.sessionId);

      if (record.isComposer) {
        setLocalStorageValue("activeComposerSessionId", record.sessionId);
        kwaiPilotBridgeAPI.updateState(
          WorkspaceState.ACTIVE_COMPOSER_SESSION_ID,
          record.sessionId,
        );
        navigate({ pathname: "/composer" });
      }
      else {
        setSessionToLocal(record.sessionId);
        kwaiPilotBridgeAPI.updateState(
          WorkspaceState.ACTIVE_SESSION_ID,
          record.sessionId,
        );
        navigate({ pathname: "/chat" });
      }
    },
    [navigate, selectComposerV2History],
  );

  const workspaceSession = useMemo(() => {
    const workspace: { current: BriefSessionItem[]; other: BriefSessionItem[] } = {
      current: [],
      other: [],
    };
    historyList.map((session) => {
      if (session.workspaceUri === workspaceUri) {
        workspace.current.push(session);
      }
      else {
        workspace.other.push(session);
      }
    });
    return workspace;
  }, [historyList, workspaceUri]);

  const handleDeleteHistory = useCallback((sessionId: string) => {
    const newHistoryList = historyList.filter(
      record => record.sessionId !== sessionId,
    );
    setHistoryList(newHistoryList);
  }
  , [historyList, setHistoryList]);

  const Tab = ({
    list,
    title,
    setList,
    fullDate = false,
  }: {
    list: BriefSessionItem[];
    title: string;
    setList: (id: string) => void;
    fullDate?: boolean;
  }) => {
    const [isOpen, setIsOpen] = useState<boolean>(true);
    const deleteHistory = (sessionId: string) => {
      setList(sessionId);
    };
    const { value: activeComposerSession } = useAsync(() => getActiveSessionIdFromStorage(), []);
    return list.length === 0
      ? null
      : (
          <div className="flex flex-col gap-2">
            <div
              className="flex items-center cursor-pointer text-foreground"
              onClick={() => setIsOpen(v => !v)}
            >
              <div className="cursor-pointer w-[24px] h-[24px] rounded-[4px] flex items-center justify-center">
                <LeftArrowIcon
                  className={`${isOpen ? "-rotate-90" : "rotate-180"}`}
                />
              </div>
              {title}
            </div>
            {isOpen
            && list.map(record => (
              <Record
                record={record}
                key={record.sessionId}
                fullDate={fullDate}
                selectHistory={selectHistory}
                editingSession={editingSession}
                setEditSession={setEditSession}
                deleteHistory={deleteHistory}
                activeComposerSession={activeComposerSession || ""}
              />
            ))}
          </div>
        );
  };
  if (workspaceUriLoading) {
    return null;
  }
  return noHistory
    ? (
        <div className="flex w-full items-center justify-center mt-[12vh] flex-col">
          <NoHistoryIcon />
          <div className="text-[13px] text-text-common-tertiary leading-[19.5px]">
            {t("common.noResults")}
          </div>
        </div>
      )
    : (
        <div className="flex flex-col gap-6">
          <Tab list={workspaceSession.current} setList={handleDeleteHistory} title={t("history.currentWorkspace")} />
          <Tab list={workspaceSession.other} setList={handleDeleteHistory} title={t("history.otherWorkspace")} />
          <div className="w-full flex items-center gap-3 justify-center">
            <div className="text-[13px] leading-[19.5px] text-text-common-tertiary">
              {t("history.allShown")}
            </div>
          </div>
        </div>
      );
};

export default HistoryContent;
