import { kwaiPilotBridgeAPI } from "@/bridge";
import { SingleIcon } from "@/components/SingleIcon";
import { Icon } from "@/components/Union/t-iconify";
import { useHistoryStore } from "@/store/history";
import { getRecordStoreByVendor } from "@/store/record";
import { t } from "i18next";

const ClearHistory = () => {
  const setHistoryList = useHistoryStore(state => state.setHistoryList);
  const chatVendorRecordStore = getRecordStoreByVendor("chat");
  const composerVendorRecordStore = getRecordStoreByVendor("composer");

  const clearHistory = () => {
    setHistoryList([]);
    chatVendorRecordStore.getState().setActiveSession({ value: "" });
    composerVendorRecordStore.getState().setActiveSession({ value: "" });
    kwaiPilotBridgeAPI.clearSession();
  };
  return (
    <SingleIcon
      onClick={clearHistory}
      className="size-[20px]"
      title={t("history.clear")}
    >
      <Icon icon="codicon:trash" />
    </SingleIcon>

  );
};

export default ClearHistory;
