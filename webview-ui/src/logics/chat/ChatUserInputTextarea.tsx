import { Placeholder } from "@/components/TextArea/components/Placeholder";
import { UserInputTextarea, UserInputTextareaProps } from "../UserInputTextarea/UserInputTextArea";
import { ModelSelector } from "@/components/TextArea/components/ModelSelector";
import { UploadFileBtn } from "./UploadFileBtn";

export const ChatUserInputTextarea: React.FC<Omit<UserInputTextareaProps, "placeholder" | "moreOpt" | "mode" | "action">> = (props) => {
  return (
    <UserInputTextarea.Root contextStorageService={props.contextStorageService} role={props.role} localMessage={props.localMessage} sessionId={props.sessionId}>
      <UserInputTextarea
        contextStorageService={props.contextStorageService}
        placeholder={(
          <Placeholder />
        )}
        moreOpt={(
          <div className="w-full  rounded-b-[7.5px]">
            <ModelSelector placement="top-start" className="py-[2px]" />
          </div>
        )}
        action={(
          <UploadFileBtn />
        )}
        mode="chat"
        {...props}
      >
      </UserInputTextarea>
    </UserInputTextarea.Root>
  );
};
