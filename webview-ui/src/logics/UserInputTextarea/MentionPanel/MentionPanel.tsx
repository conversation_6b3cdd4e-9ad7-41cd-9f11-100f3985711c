import clsx from "clsx";
import { forwardRef, ReactNode, useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";

import { RichEditorMenuType } from "@/components/TextArea/const";
import { useMergeRefs } from "@chakra-ui/react";
import { MenuList } from "./MenuList";
import { CustomPromptData } from "shared/lib/CustomVariable";
import { SerializedCustomPromptData, useRecentUsedPrompts } from "@/components/TextArea/components/NewPanel/useRecentUsedPrompts";
import { SharpCommand } from "@shared/types";
import { useTypeaheadMenuContext } from "@/components/TextArea/lexical/MentionsV2PluginContext";
import { MentionTypeaheadOption } from "./useOptions";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { MentionPanelContext } from "./MentionPanelContext";
import { kwaiPilotBridgeAPI } from "@/bridge";

export interface PanelHeaderData {
  title: string | JSX.Element;
  description: string;
  icon: ReactNode;
}
export type NewPanelFirstStageMenuType = RichEditorMenuType.SHARP_COMMAND | RichEditorMenuType.SLASH_COMMAND;

export function NewPanelProvider(props: { children: ReactNode }) {
  return (
    <MentionPanelContext.Provider value={null}>
      {props.children}
    </MentionPanelContext.Provider>
  );
}

export interface MentionPanelProps {
  /**
   * 选择模式
   * unique: 只选择之前没有选过的元素，选择过的元素再次选择会取消
   * any: 可以选择任意元素不管之前有没有选过
   */
  selectMode: "unique" | "any";
  query: string;
  filterSharpCommandKeyList?: SharpCommand[];
  options: MentionTypeaheadOption[];
  bottomElement?: JSX.Element | null;
}

export type MentionPanelContextState = Required<MentionPanelProps> & {
  selectIndex: number | "header";
  /* 有些选项需要等待异步操作完成， 此时需要将 index 记录下来 */
  submittingIndex: number | null;
  onSubmittingIndexChange: (submittingIndex: number | null) => void;
  handleSelectMenu: (menu: MentionTypeaheadOption, index: number) => void;
  onIsKeyboardEventChange: (isKeyboardEvent: boolean) => void;
  reportCustomPromptRecentUsed: (data: CustomPromptData) => void;
  recentUsedPrompts: SerializedCustomPromptData[];
};

const _MentionPanel = forwardRef<HTMLDivElement>((_, forwardedRef) => {
  const contextState = useContext(MentionPanelContext);

  if (!contextState) {
    throw new Error("NewPanelContext is not provided");
  }

  const setCodeSearchDefaultFiles = useRichEditPanelMenuStore(state => state.setCodeSearchDefaultFiles);
  const setCodeSearchDefaultDir = useRichEditPanelMenuStore(state => state.setCodeSearchDefaultDir);

  const fetchDefaultFileAndDir = useCallback(async () => {
    const defaultFiles = await kwaiPilotBridgeAPI.extensionMisc.$getMentionDefaultFiles();
    const defaultDirs = await kwaiPilotBridgeAPI.extensionMisc.$getMentionDefaultDirectories();
    setCodeSearchDefaultFiles(defaultFiles);
    setCodeSearchDefaultDir(defaultDirs);
  }, [setCodeSearchDefaultFiles, setCodeSearchDefaultDir]);

  useEffect(() => {
    fetchDefaultFileAndDir();
  }, [fetchDefaultFileAndDir]);
  const rootFocusableRef = useRef<HTMLDivElement>(null);

  const [, setIsPanelFocused] = useState(false);

  const composedRef = useMergeRefs(rootFocusableRef, forwardedRef);

  return (
    <div
      ref={composedRef}
      tabIndex={-1}
      onFocus={() => setIsPanelFocused(true)}
      onBlur={() => setIsPanelFocused(false)}
      onMouseDown={e => (e.target as HTMLElement).focus()}
      className={clsx(
        ` z-30 bg-editor-background text-[13px] leading-[18px] overflow-hidden rounded-[6px] border-[0.5px] border-settings-dropdownBorder `,
      )}
    >
      <MenuList>
      </MenuList>
      {contextState.bottomElement}
    </div>
  );
});

export const MentionPanel = forwardRef<HTMLElement, MentionPanelProps>((props, forwardedRef) => {
  const {
    query,
    filterSharpCommandKeyList,
    options,
    bottomElement,
    selectMode,
  } = props;

  const { itemProps: { selectedIndex: typeaheadMenuSelectedIndex, setHighlightedIndex, selectOptionAndCleanUp }, currentMenu } = useTypeaheadMenuContext();
  const selectIndex = useMemo<number | /* 表明选中的是标题区域, 点击后会返回 */"header">(() => {
    if (typeaheadMenuSelectedIndex === null) {
      return -1;
    }
    return typeaheadMenuSelectedIndex;
  }, [typeaheadMenuSelectedIndex]);

  useEffect(() => {
    setHighlightedIndex(0);
  }, [currentMenu, setHighlightedIndex]);

  const [submittingIndex, setSubmittingIndex] = useState<number | null>(null);

  const { reportCustomPromptRecentUsed, recentUsedPrompts } = useRecentUsedPrompts();

  const handleSelectMenu = useCallback(
    (menu: MentionTypeaheadOption, index: number) => {
      setHighlightedIndex(index);
      selectOptionAndCleanUp(menu);
    },
    [setHighlightedIndex, selectOptionAndCleanUp],
  );
  const [isKeyboardEvent, setIsKeyboardEvent] = useState(false);

  const contextState = useMemo<Required<MentionPanelContextState>>(() => ({
    query,
    selectIndex,
    handleSelectMenu,
    isKeyboardEvent,
    onIsKeyboardEventChange: setIsKeyboardEvent,
    submittingIndex,
    onSubmittingIndexChange: setSubmittingIndex,
    reportCustomPromptRecentUsed, recentUsedPrompts,
    filterSharpCommandKeyList: filterSharpCommandKeyList ?? [],
    options,
    bottomElement: bottomElement ?? null,
    selectMode,
  }), [query, selectIndex, handleSelectMenu, isKeyboardEvent, submittingIndex, reportCustomPromptRecentUsed, recentUsedPrompts, filterSharpCommandKeyList, options, bottomElement, selectMode]);

  const collectionRef = useRef<HTMLElement | null>(null);

  const composedRef = useMergeRefs(collectionRef, forwardedRef);

  return (
    <MentionPanelContext.Provider value={contextState}>
      <_MentionPanel {...props} ref={composedRef} />
    </MentionPanelContext.Provider>
  );
});
