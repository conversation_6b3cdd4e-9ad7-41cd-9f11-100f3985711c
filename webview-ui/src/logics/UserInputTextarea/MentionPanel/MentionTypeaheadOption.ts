import { FilenameHighlightMatch } from "@/logics/composer/tools/components/FilenameDisplay";
import { MenuOption } from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { CustomPromptData } from "shared/lib/CustomVariable";
import {
  MentionNodeV2Structure_File,
  MentionNodeV2Structure_Knowledge,
  MentionNodeV2Structure_Rule,
  MentionNodeV2Structure_SlashCommand,
  MentionNodeV2Structure_Tree,
} from "shared/lib/MentionNodeV2/nodes";

export enum TypeaheadMenuOptionType {
  /** 带子菜单的 */
  heading = "heading",
  /** 文件 */
  file = "file",
  /** 文件夹 */
  folder = "folder",
  /** 项目规则 */
  rule = "rule",
  /** 添加新的规则 */
  addRule = "addRule",
  /** 初始状态一级面板 */
  none = "none",
  /** 头部 */
  header = "header",
  /** 自定义指令 */
  customPrompt = "customPrompt",
  /** 快捷指令 */
  slashCommand = "slashCommand",
  /** flag 开启联网 */
  web = "web",
  /** 对话模式代码库 codebase */
  codebase = "codebase",
  /** 对话模式知识库 knowledge */
  knowledge = "knowledge",
}

export class MentionTypeaheadOption<T extends TypeaheadMenuOptionType = TypeaheadMenuOptionType > extends MenuOption {
  type: T;
  name: string;
  secondaryText: string;
  disabled: boolean = false;

  nameHighlightMatches: FilenameHighlightMatch[] = [];

  structure: T extends TypeaheadMenuOptionType.file
    ? MentionNodeV2Structure_File
    : T extends TypeaheadMenuOptionType.folder
      ? MentionNodeV2Structure_Tree
      : T extends TypeaheadMenuOptionType.customPrompt
        ? CustomPromptData
        : T extends TypeaheadMenuOptionType.rule
          ? MentionNodeV2Structure_Rule
          : T extends TypeaheadMenuOptionType.addRule
            ? undefined
            : T extends TypeaheadMenuOptionType.web
              ? null
              : T extends TypeaheadMenuOptionType.knowledge
                ? MentionNodeV2Structure_Knowledge
                : T extends TypeaheadMenuOptionType.slashCommand
                  ? MentionNodeV2Structure_SlashCommand
                  : null;

  /**
   * @deprecated 使用 builder 创建
   * @param type
   * @param name
   * @param secondaryText
   * @param structure
   */
  constructor(type: T, name: string, secondaryText: string, structure: MentionTypeaheadOption<T>["structure"]);
  constructor(builder: MentionTypeaheadOptionBuilder<T>);
  constructor(
    typeOrBuilder: T | MentionTypeaheadOptionBuilder<T>,
    name?: string,
    secondaryText?: string,
    structure?: MentionTypeaheadOption<T>["structure"],
  ) {
    if (typeOrBuilder instanceof MentionTypeaheadOptionBuilder) {
      super(typeOrBuilder.name);
      this.type = typeOrBuilder.type;
      this.name = typeOrBuilder.name;
      this.secondaryText = typeOrBuilder.secondaryText;
      this.structure = typeOrBuilder.structure;
      this.disabled = typeOrBuilder.disabled;
      this.nameHighlightMatches = typeOrBuilder.nameHighlightMatches;
    }
    else {
      super(name!);
      this.type = typeOrBuilder;
      this.name = name!;
      this.secondaryText = secondaryText!;
      this.structure = structure!;
    }
  }

  static builder<T extends TypeaheadMenuOptionType>() {
    return new MentionTypeaheadOptionBuilder<T>();
  }
}

export class MentionTypeaheadOptionBuilder<T extends TypeaheadMenuOptionType> {
  type!: T;
  name: string = "";
  secondaryText: string = "";
  disabled: boolean = false;
  structure!: MentionTypeaheadOption<T>["structure"];
  nameHighlightMatches: FilenameHighlightMatch[] = [];

  setType(type: T) {
    this.type = type;
    return this;
  }

  setName(name: string) {
    this.name = name;
    return this;
  }

  setSecondaryText(text: string) {
    this.secondaryText = text;
    return this;
  }

  setDisabled(disabled: boolean) {
    this.disabled = disabled;
    return this;
  }

  setNameHighlightMatches(matches: FilenameHighlightMatch[]) {
    this.nameHighlightMatches = matches;
    return this;
  }

  setStructure(structure: MentionTypeaheadOption<T>["structure"]) {
    this.structure = structure;
    return this;
  }

  build(): MentionTypeaheadOption<T> {
    if (!this.type) {
      throw new Error("Type is required");
    }
    if (!this.name) {
      throw new Error("Name is required");
    }
    return new MentionTypeaheadOption<T>(this);
  }
}
