import { useAsyncFn } from "react-use";
import { use<PERSON>emo, useEffect } from "react";
import { RichEditorBoxPanelData } from "shared/lib/richEditor/const";
import { CommandPrefix, PromptConfig, SharpCommand, SlashCommand } from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { warnNeverType } from "@/utils/throwUnknownError";
import { slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { basename, dirname } from "path-browserify";
import { getSortFileOrDir } from "@/components/TextArea/utils";
import { useRecentUsedPrompts } from "@/components/TextArea/components/NewPanel/useRecentUsedPrompts";
import { usePromptTemplate } from "@/store/promptTemplate";
import { SelectionOrFileContext } from "shared/lib/bridge/protocol-observable";
import { Doc } from "shared/lib/business";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { URI } from "vscode-uri";
import debounce from "lodash/debounce";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import { MentionTypeaheadOption, TypeaheadMenuOptionType } from "./MentionTypeaheadOption";
import { t } from "i18next";

/**
 * 文件搜索 hook
 */
function useFileSearch() {
  const [{ value: searchResults = [] }, doSearch] = useAsyncFn(async (query: string) => {
    if (!query) {
      return [];
    }

    try {
      const result = await kwaiPilotBridgeAPI.extensionMisc.$doFileSearch(query);
      return result.results.map(({ uri, ...rest }) => ({
        ...rest,
        uri: URI.revive(uri),
      }));
    }
    catch (error) {
      console.error("File search failed:", error);
      return [];
    }
  }, []);

  // 创建防抖函数
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      if (query) {
        doSearch(query);
      }
    }, 300),
    [doSearch],
  );

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  return {
    searchResults,
    doSearch: debouncedSearch,
  };
}

/**
 * 文件搜索 hook
 */
function useDirectorySearch() {
  const [{ value: searchResults = [] }, doSearch] = useAsyncFn(async (query: string) => {
    if (!query) {
      return [];
    }

    try {
      const result = await kwaiPilotBridgeAPI.extensionMisc.$doDirSearch(query);
      return result.results.map(({ uri, ...rest }) => ({
        ...rest,
        uri: URI.revive(uri),
      }));
    }
    catch (error) {
      console.error("Directory search failed:", error);
      return [];
    }
  }, []);

  // 创建防抖函数
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      if (query) {
        doSearch(query);
      }
    }, 300),
    [doSearch],
  );

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  return {
    searchResults,
    doSearch: debouncedSearch,
  };
}

export {
  TypeaheadMenuOptionType,
  MentionTypeaheadOption,
};

const getDefaultMentionOptions = ({
  mode,
}: {
  mode: "chat" | "composer";
}) => {
  const options: MentionTypeaheadOption<any>[] = [
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.file, t("mention.file"), null),
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.folder, t("mention.folder"), null),
    mode === "chat" ? new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.knowledge, t("mention.knowledge"), null) : undefined,
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.rule, t("mention.rule"), null),
    mode === "chat" ? new MentionTypeaheadOption(TypeaheadMenuOptionType.codebase, "", "", null) : undefined,
    mode === "chat" ? new MentionTypeaheadOption(TypeaheadMenuOptionType.web, "", "", null) : undefined,
  ].filter((v): v is MentionTypeaheadOption<any> => Boolean(v));
  return options;
};

function RuleBoxPanelData2MentionTypeaheadOption(data: RichEditorBoxPanelData): MentionTypeaheadOption {
  return new MentionTypeaheadOption(
    MentionTypeaheadOption.builder<TypeaheadMenuOptionType.rule>()
      .setType(TypeaheadMenuOptionType.rule)
      .setName(data.title)
      .setSecondaryText(data.description)
      .setStructure({
        type: "rule",
        uri: data.uri,
        relativePath: data.data,
      }),
  );
}

export function useMentionOptions({ currentMenu, queryString, mode }: {
  currentMenu: TypeaheadMenuOptionType;
  queryString: string;
  mode: "chat" | "composer";
}) {
  const { searchResults: fileSearchResults, doSearch: doFileSearch } = useFileSearch();
  const { searchResults: directorySearchResults, doSearch: doDirectorySearch } = useDirectorySearch();

  // 当搜索词变化时执行搜索
  useEffect(() => {
    if (currentMenu === TypeaheadMenuOptionType.file && queryString) {
      doFileSearch(queryString);
    }
    else if (currentMenu === TypeaheadMenuOptionType.folder && queryString) {
      doDirectorySearch(queryString);
    }
    else if (currentMenu === TypeaheadMenuOptionType.none && queryString) {
      doFileSearch(queryString);
      doDirectorySearch(queryString);
    }
  }, [currentMenu, queryString, doFileSearch, doDirectorySearch]);

  const codeSearchDefaultDir = useRichEditPanelMenuStore(state => state.codeSearchDefaultDir);
  const codeSearchDefaultFiles = useRichEditPanelMenuStore(state => state.codeSearchDefaultFiles);
  const ruleFiles = useRichEditPanelMenuStore(state => state.ruleFiles);

  const disabledMenu = useRichEditPanelMenuStore(state => state.disabledMenu);
  const knowledgeList = useRichEditPanelMenuStore(state => state.docList);

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");

  const options = useMemo<MentionTypeaheadOption[]>(() => {
    if ((queryString === null || queryString === "") && currentMenu === TypeaheadMenuOptionType.none) {
      return getDefaultMentionOptions({ mode });
    }
    else if (currentMenu === TypeaheadMenuOptionType.none) {
      type DocOrBoxPanelDataSearch = { doc: Doc; title: string } | { boxPanelData: RichEditorBoxPanelData; title: string };
      // 处理带 query 的情况
      const fileOrDirectorySearchResults: MentionTypeaheadOption[] = [
        ...fileSearchResults.map(file => new MentionTypeaheadOption(
          MentionTypeaheadOption.builder<TypeaheadMenuOptionType.file>()
            .setType(TypeaheadMenuOptionType.file)
            .setName(file.label)
            .setSecondaryText(file.relativePath)
            .setStructure({
              type: "file",
              uri: file.uri.toString(),
              relativePath: file.relativePath,
            })
            .setNameHighlightMatches(file.highlights.label),
        )),
      ];
      if (!disabledMenu[SharpCommand.FOLDER].status) {
        fileOrDirectorySearchResults.push(...directorySearchResults.map(dir => new MentionTypeaheadOption(
          MentionTypeaheadOption.builder<TypeaheadMenuOptionType.folder>()
            .setType(TypeaheadMenuOptionType.folder)
            .setName(dir.label)
            .setSecondaryText(dir.relativePath)
            .setStructure({
              type: "tree",
              uri: dir.uri.toString(),
              relativePath: dir.relativePath,
            })
            .setNameHighlightMatches(dir.highlights.label),
        )));
      }
      const searchCandidates: DocOrBoxPanelDataSearch[] = [];
      searchCandidates.push(
        ...ruleFiles.map(v => ({ boxPanelData: v, title: v.title })),
      );
      if (mode === "chat") {
        searchCandidates.push(...knowledgeList.map(doc => ({ doc, title: doc.name })));
      }
      return [
        ...fileOrDirectorySearchResults,
        ...getSortFileOrDir(searchCandidates, queryString)
          .map(searchItem => "boxPanelData" in searchItem
            ? RuleBoxPanelData2MentionTypeaheadOption(searchItem.boxPanelData)
            : knownledgeDoc2MentionTypeaheadOption(searchItem.doc),
          ),
      ];
    }
    else {
      switch (currentMenu) {
        case TypeaheadMenuOptionType.file: {
          if (!queryString) {
            let fileList = codeSearchDefaultFiles.map(file => new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
              TypeaheadMenuOptionType.file,
              basename(file.relativePath),
              dirname(file.relativePath) === "." ? "" : dirname(file.relativePath),
              {
                type: "file",
                uri: URI.revive(file.uri).toString(),
                relativePath: file.relativePath,
              },
            ));
            if (currentFileAndSelection) {
              fileList = [
                new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
                  TypeaheadMenuOptionType.file,
                  basename(currentFileAndSelection.relativePath),
                  currentFileAndSelection.relativePath,
                  {
                    type: "file",
                    uri: currentFileAndSelection.uri,
                    relativePath: currentFileAndSelection.relativePath,
                  },
                ),
                ...fileList.filter(v => (v as MentionTypeaheadOption<TypeaheadMenuOptionType.file>).structure.uri !== currentFileAndSelection.uri),
              ];
            }
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("mention.file"), "", null),
              ...fileList,
            ];
          }
          else {
            return [
              new MentionTypeaheadOption(
                MentionTypeaheadOption.builder()
                  .setType(TypeaheadMenuOptionType.header)
                  .setName(t("mention.file")),
              ),
              ...fileSearchResults.map(({ uri, relativePath, label, highlights }) => new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
                MentionTypeaheadOption.builder<TypeaheadMenuOptionType.file>()
                  .setType(TypeaheadMenuOptionType.file)
                  .setName(label)
                  .setSecondaryText(relativePath)
                  .setStructure(
                    {
                      type: "file",
                      uri: uri.toString(),
                      relativePath,
                    },
                  )
                  .setNameHighlightMatches(highlights.label),
              )),
            ];
          }
        }
        case TypeaheadMenuOptionType.folder: {
          if (!queryString) {
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("mention.folder"), "", null),
              ...codeSearchDefaultDir.map(dir => new MentionTypeaheadOption<TypeaheadMenuOptionType.folder>(
                TypeaheadMenuOptionType.folder,
                basename(dir.relativePath),
                dir.relativePath,
                {
                  type: "tree",
                  uri: URI.revive(dir.uri).toString(),
                  relativePath: dir.relativePath,
                },
              )),
            ];
          }
          else {
            return [
              new MentionTypeaheadOption(MentionTypeaheadOption.builder()
                .setType(TypeaheadMenuOptionType.header)
                .setName(t("mention.folder")),
              ),
              ...directorySearchResults.map(({ uri, relativePath, label, highlights }) => new MentionTypeaheadOption<TypeaheadMenuOptionType.folder>(
                MentionTypeaheadOption.builder<TypeaheadMenuOptionType.folder>()
                  .setType(TypeaheadMenuOptionType.folder)
                  .setName(label)
                  .setSecondaryText(relativePath)
                  .setStructure(
                    {
                      type: "tree",
                      uri: uri.toString(),
                      relativePath,
                    },
                  )
                  .setNameHighlightMatches(highlights.label),
              )),
            ];
          }
        }
        case TypeaheadMenuOptionType.knowledge: {
          const header = new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("mention.knowledge"), "", null);
          if (!queryString) {
            return [
              header,
              ...knowledgeList.map(doc => new MentionTypeaheadOption<TypeaheadMenuOptionType.knowledge>(
                TypeaheadMenuOptionType.knowledge,
                doc.name,
                "",
                {
                  type: "knowledge",
                  uri: `knowledge://${doc.id}`,
                  relativePath: "",
                  doc,
                },
              )),
            ];
          }
          else {
            return [
              header,
              ...knowledgeList
                .filter(doc => doc.name.toLowerCase().includes(queryString.toLowerCase()))
                .map(doc => knownledgeDoc2MentionTypeaheadOption(doc)),
            ];
          }
        }
        case TypeaheadMenuOptionType.rule: {
          if (!queryString) {
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("mention.rule"), "", null),
              ...ruleFiles.map(RuleBoxPanelData2MentionTypeaheadOption),
            ];
          }
          else {
            const rawList = ruleFiles || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("mention.rule"), "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(RuleBoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
      }
    }
    return [];
  }, [queryString, currentMenu, mode, disabledMenu, ruleFiles, knowledgeList, codeSearchDefaultFiles, currentFileAndSelection, fileSearchResults, codeSearchDefaultDir, directorySearchResults]);
  return {
    options,
  };
}

interface SlashCommandStructure {
  title: string;
  key: SlashCommand;
  search: string[];

}

const getSlashCommands = () => ([
  {
    title: t("slashCommand.funcComment"),
    key: SlashCommand.FUNC_COMMENT,
    search: [
      "函数注释",
      "hanshuzhushi",
      "hszs",
      "zs",
      "s",
      "注释",
      "释",
      "zhushi",
      "数",
      t("slashCommand.funcComment"),
    ],
  },
  {
    title: t("slashCommand.lineCodeComment"),
    key: SlashCommand.LINE_CODE_COMMENT,
    search: [
      "行间注释",
      "hangjianzhushi",
      "hjzs",
      "zs",
      "s",
      "注释",
      "释",
      "zhushi",
      "间",
      "j",
      t("slashCommand.lineCodeComment"),
    ],
  },
  {
    title: t("slashCommand.codeExplain"),
    key: SlashCommand.CODE_EXPLAIN,
    search: [
      "代码解释",
      "daimajieshi",
      "dmjs",
      "js",
      "s",
      "解释",
      "释",
      "jieshi",
      "码",
      "m",
      t("slashCommand.codeExplain"),
    ],
  },
  {
    title: t("slashCommand.codeRefactor"),
    key: SlashCommand.CODE_REFACTOR,
    search: [
      "代码优化",
      "daimayouhua",
      "dmyh",
      "yh",
      "h",
      "优化",
      "化",
      "youhua",
      "码",
      "m",
      t("slashCommand.codeRefactor"),
    ],
  },
  {
    title: t("slashCommand.funcSplit"),
    key: SlashCommand.FUNC_SPLIT,
    search: [
      "函数拆分",
      "hanshuchaifen",
      "hscf",
      "cf",
      "f",
      "拆分",
      "分",
      "chaifen",
      "数",
      "s",
      t("slashCommand.funcSplit"),
    ],
  },
  {
    title: t("slashCommand.unitTest"),
    key: SlashCommand.UNIT_TEST,
    search: [
      "单元测试",
      "danyuanceshi",
      "dycs",
      "cs",
      "s",
      "测试",
      "试",
      "ceshi",
      "元",
      "y",
      t("slashCommand.unitTest"),
    ],
  },

]);

const getCustomPromptCommand = (): SlashCommandStructure => ({
  title: t("slashCommand.customPrompt"),
  key: SlashCommand.CUSTOM_PROMPT,
  search: [
    "自定义指令",
    "zidingyizhiling",
    "zdyzl",
    "zdzl",
    "zl",
    "自定义",
    "zidingyi",
    "zdy",
    "zdy",
    "指令",
    "zhiling",
    "zl",
    t("slashCommand.customPrompt"),
  ],
});

const getClearContextCommand = (): SlashCommandStructure => ({
  title: t("slashCommand.clearContext"),
  key: SlashCommand.CLEAR_CONVERSATION,
  search: [
    "清除上下文",
    "qingchushangxiawen",
    "qcsxw",
    "sxw",
    "w",
    "上下文",
    "清除",
    "shangxiawen",
    "qing",
    "qingchu",
    t("slashCommand.clearContext"),
  ],
});

function slashCommandStructure2MentionTypeaheadOption(
  structure: SlashCommandStructure,
  promptTemplates: PromptConfig[],
  currentFileAndSelection: SelectionOrFileContext | null | undefined,
): MentionTypeaheadOption<any> {
  return new MentionTypeaheadOption(
    TypeaheadMenuOptionType.slashCommand, "", "",
    {
      type: "slashCommand",
      uri: `slashCommand://${structure.key as SlashCommand}`,
      command: structure.key,
      relativePath: "",
      label: structure.title,
      template: promptTemplates.find(v => structure.key === CommandPrefix.SLASH + v.key)?.template || "",
      contextItem: slashCommandSetRequiringContextItem.has(structure.key)
        ? currentFileAndSelection?.range
          ? {
              type: "selection",
              content: currentFileAndSelection.content,
              uri: currentFileAndSelection.uri,
              relativePath: currentFileAndSelection.relativePath,
              range: currentFileAndSelection.range,
            }
          : currentFileAndSelection
            ? {
                type: "file",
                uri: currentFileAndSelection.uri || "",
                relativePath: currentFileAndSelection.relativePath || "",
              }
            : undefined
        : undefined,
    });
}

function slashCommandMatchQuery(structure: SlashCommandStructure, queryString: string): boolean {
  return structure.search?.some(searchItem =>
    searchItem.toLowerCase().startsWith(queryString.toLowerCase()),
  );
}

export function useSlashOptions({
  currentMenu,
  queryString,
}: {

  currentMenu: TypeaheadMenuOptionType;
  queryString: string;
  /**
   * 对话模式和智能体模式的不同
   *
   * * 对话模式有代码库\知识库\自定义指令
   */
  mode: "chat" | "composer";
}) {
  const customPrompts = useRichEditPanelMenuStore(state => state.customPrompts);

  const promptTemplate = usePromptTemplate(s => s.promptTemplate);

  const { recentUsedPrompts } = useRecentUsedPrompts();
  const recentUsedPromptIds = useMemo(() => {
    return recentUsedPrompts.map(item => item.id);
  }, [recentUsedPrompts]);
  const slashCommands = getSlashCommands();
  const customPromptCommand = getCustomPromptCommand();
  const clearContextCommand = getClearContextCommand();

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");
  const [, isKwaiPilotIDE] = useIdeEnv();
  const options = useMemo<MentionTypeaheadOption[]>(() => {
    if ((queryString === null || queryString === "") && currentMenu === TypeaheadMenuOptionType.none) {
      const header = new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("slashCommand.command"), "", null);
      // 第一层的元素不能返回
      header.disabled = true;
      const options: MentionTypeaheadOption<any>[] = [
        header,
        ...slashCommands.map(item => slashCommandStructure2MentionTypeaheadOption(item, promptTemplate, currentFileAndSelection)),
        !isKwaiPilotIDE && new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.customPrompt, "", null),
        slashCommandStructure2MentionTypeaheadOption(clearContextCommand, promptTemplate, currentFileAndSelection),
      ].filter((v): v is MentionTypeaheadOption<any> => Boolean(v));
      return options;
    }
    else if (currentMenu === TypeaheadMenuOptionType.none) {
      const options = slashCommands
        .filter(v => slashCommandMatchQuery(v, queryString))
        .map(v => slashCommandStructure2MentionTypeaheadOption(v, promptTemplate, currentFileAndSelection));
      if (slashCommandMatchQuery(customPromptCommand, queryString)) {
        options.push(new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.customPrompt, "", null));
      }
      if (slashCommandMatchQuery(clearContextCommand, queryString)) {
        options.push(slashCommandStructure2MentionTypeaheadOption(clearContextCommand, promptTemplate, currentFileAndSelection));
      }
      return options;
    }
    else {
      switch (currentMenu) {
        case TypeaheadMenuOptionType.customPrompt: {
          const customPromptsHitResult = getSortFileOrDir(customPrompts, queryString).map(
            prompt =>
              new MentionTypeaheadOption(
                TypeaheadMenuOptionType.customPrompt,
                prompt.data,
                prompt.description,
                prompt.raw,
              ),
          );

          const recentUsedPrompts = customPromptsHitResult.filter(item =>
            recentUsedPromptIds.includes(item.structure.id),
          );
          const restPrompts = customPromptsHitResult.filter(
            item => !recentUsedPromptIds.includes(item.structure.id),
          );

          return [
            new MentionTypeaheadOption(TypeaheadMenuOptionType.header, t("slashCommand.customPrompt"), "", null),
            ...recentUsedPrompts,
            ...restPrompts,
          ];
        }
        case TypeaheadMenuOptionType.file:
        case TypeaheadMenuOptionType.addRule:
        case TypeaheadMenuOptionType.codebase:
        case TypeaheadMenuOptionType.header:
        case TypeaheadMenuOptionType.heading:
        case TypeaheadMenuOptionType.knowledge:
        case TypeaheadMenuOptionType.rule:
        case TypeaheadMenuOptionType.web:
        case TypeaheadMenuOptionType.slashCommand:
        case TypeaheadMenuOptionType.folder: {
          // 不处理
          return [];
        }
        default: {
          const neverType = currentMenu;
          warnNeverType(neverType);
          return [];
        }
      }
    }
  }, [currentFileAndSelection, currentMenu, customPrompts, promptTemplate, queryString, recentUsedPromptIds, isKwaiPilotIDE, slashCommands, clearContextCommand, customPromptCommand]);
  return {
    options,
  };
}
function knownledgeDoc2MentionTypeaheadOption(doc: Doc): MentionTypeaheadOption<TypeaheadMenuOptionType.knowledge> {
  return new MentionTypeaheadOption<TypeaheadMenuOptionType.knowledge>(
    TypeaheadMenuOptionType.knowledge,
    doc.name,
    "",
    {
      type: "knowledge",
      uri: `knowledge://${doc.id}`,
      relativePath: "",
      doc,
    },
  );
}
