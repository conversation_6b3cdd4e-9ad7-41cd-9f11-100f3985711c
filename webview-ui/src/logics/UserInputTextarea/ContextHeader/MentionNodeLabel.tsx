import { getIcon } from "@/utils/fileIcon";
import { Tooltip } from "@/components/Union/chakra-ui";
import { Box, Flex, useSize } from "@chakra-ui/react";
import { Icon } from "@/components/Union/t-iconify";
import { LexicalEditor } from "lexical";
import { ReactNode, useRef } from "react";
import { basename } from "path-browserify";
import KidIcon from "@/components/Union/kid";
import { t } from "i18next";

import IconClose from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_close";
import { ContextHeaderItem, isIdentical, useContextHeaderContext } from "./ContextHeaderContext";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ConfigProvider } from "antd";
import { MentionNodeV2Structure, MentionNodeV2Structure_Knowledge, MentionNodeV2Structure_SlashCommand } from "shared/lib/MentionNodeV2/nodes";
import { displayLineRange } from "shared/lib/CustomVariable/cody-shared/range";
import { FilenameDisplay } from "@/logics/composer/tools/components/FilenameDisplay";
import { collectMentionNodeV2 } from "./collectMentionNode";
import { warnNeverType } from "@/utils/throwUnknownError";
import clsx from "clsx";
import AutoTooltip from "@/components/AutoTooltip";
import { IconTerminal } from "@/components/Icons";

const fileStructureTypes = ["file", "remoteFile", "remoteImage", "tree", "selection", "rule"] as const satisfies MentionNodeV2Structure["type"][];

function useFileComponent(type: MentionNodeV2Structure["type"]): type is (typeof fileStructureTypes)[number] {
  return fileStructureTypes.includes(type as any);
}

function getMentionStructureIcon(structure: MentionNodeV2Structure): string {
  const { relativePath, type } = structure;
  if (type === "rule") {
    return "codicon:symbol-ruler";
  }
  if (type === "web") {
    return "codicon:globe";
  }
  if (type === "codebase") {
    return "codicon:code";
  }
  if (type === "tree") {
    return "codicon:folder";
  }
  if (type === "remoteImage") {
    return "octicon:image-24";
  }
  if (type === "file" || type === "selection" || type === "remoteFile") {
    return getIcon(relativePath, false);
  }
  if (type === "knowledge") {
    return "codicon:book";
  }
  if (type === "slashCommand") {
    return "";
  }
  warnNeverType(type, `Unexpected type: ${type}`);
  return "codicon:file"; // Fallback icon for unexpected types
}

function structureDisplayText(type: Exclude<MentionNodeV2Structure["type"], (typeof fileStructureTypes)[number]>, structure: MentionNodeV2Structure) {
  switch (type) {
    case "knowledge":
      return (structure as MentionNodeV2Structure_Knowledge).doc.name;
    case "web":
      return t("mentionNodeLabel.web");
    case "codebase":
      return t("mentionNodeLabel.codebase");
    case "slashCommand":
      return `/${(structure as MentionNodeV2Structure_SlashCommand).label}`;
    default:
      warnNeverType(type, `Unexpected type: ${type}`);
      return t("mentionNodeLabel.unknownType");
  }
}

function useMentionNodeLabelOperation({
  editor,
  node,
  onDelete,
}: {
  editor: LexicalEditor;
  node: ContextHeaderItem;
  onDelete?: () => void;
}) {
  const { state: { tryDeleteNode } } = useContextHeaderContext();
  const onCloseClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // 如果有关联的 editor 节点，先删除它
    const matchedNodes = collectMentionNodeV2(editor).filter(n => isIdentical(n.__structure, node.structure));
    if (matchedNodes.length > 0) {
      editor.update(() => {
        for (const matchedNode of matchedNodes) {
          matchedNode.remove();
        }
      });
    }

    // 更新 context state
    tryDeleteNode(node.structure);
    onDelete?.();
  };
  return {
    onCloseClick,
  };
}

export interface MentionNodeUnavailableInfo {
  reason: ReactNode;
}

// 判断是否为终端选择节点
function isTerminalSelectionNode(node: ContextHeaderItem): boolean {
  return node.structure.type === "selection" && node.structure.uri === "terminal://current";
}

// 渲染节点图标组件
function NodeIcon({ node }: { node: ContextHeaderItem }) {
  if (isTerminalSelectionNode(node)) {
    return <IconTerminal className="group-hover:hidden size-[14px]" />;
  }
  return <Icon icon={getMentionStructureIcon(node.structure)} className="group-hover:hidden size-[14px]" />;
}

export function MentionNodeLabel({ node, editor, onDelete, unavailable }: {
  node: ContextHeaderItem;
  editor: LexicalEditor;
  onDelete?: () => void;
  unavailable?: MentionNodeUnavailableInfo;
}) {
  const { relativePath, type } = node.structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  const { onCloseClick } = useMentionNodeLabelOperation({
    editor,
    node,
    onDelete,
  });

  const maxSuffixLength = node.structure.type === "selection" ? (Math.max(15 - displayLineRange(node.structure.range).length, 0)) : 15;

  return (
    <Tooltip label={unavailable ? unavailable.reason : relativePath} openDelay={700} placement="top">
      <Flex
        align="center"
        display="inline-flex"
        gap={1}
        userSelect="none"
        fontFamily="var(--vscode-font-family)"
        className="group max-w-[160px] h-[22px] flex-none  cursor-pointer pl-[4px] pr-[6px] border border-solid border-commandCenter-inactiveBorder rounded hover:bg-toolbar-hoverBackground"
        onClick={() => {
          kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(node.structure);
        }}
        overflow="hidden"
      >
        <div className=" float-none flex items-center size-[14px]">
          <NodeIcon node={node} />
          <Icon icon="codicon:close" width="12" height="12" onClick={onCloseClick} className=" flex-none group-hover:block hidden size-[14px] text-icon-foreground" />
        </div>
        <Box maxW={`${120 - (rangeRefSize?.width || 0)}px`}>
          {useFileComponent(type)
            ? <FilenameDisplay tooltip={false} filename={filename} maxSuffixLenth={maxSuffixLength} className={unavailable && " text-disabledForeground line-through"} />
            : (
                <div className=" flex items-center">
                  <AutoTooltip className={clsx("text-xs inline-block align-top", { "text-disabledForeground line-through": unavailable })} label={structureDisplayText(type, node.structure)} openDelay={700} placement="top">
                    {structureDisplayText(type, node.structure)}
                  </AutoTooltip>
                </div>
              )}
        </Box>
        {node.structure.type === "selection" && (
          <span ref={rangeRef} className={clsx(" flex-none  text-[12px]", unavailable ? "line-through text-disabledForeground" : "text-foreground")}>
            {displayLineRange(node.structure.range)}
          </span>
        )}
      </Flex>
    </Tooltip>
  );
}

export function MentionNodeLabel2({ node, editor, onDelete, unavailable }: {
  node: ContextHeaderItem;
  editor: LexicalEditor;
  onDelete?: () => void;
  unavailable?: MentionNodeUnavailableInfo;
}) {
  const { relativePath, type } = node.structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  const { onCloseClick } = useMentionNodeLabelOperation({
    editor,
    node,
    onDelete,
  });

  return (
    <ConfigProvider
      theme={{
        components: {
          Tooltip: {
            zIndexPopup: 10000/* > var(--chakra-zIndices-popover) */,
          },
        },
      }}
    >
      <Tooltip label={relativePath} openDelay={700} closeOnScroll placement="top">
        <Flex
          align="center"
          display="inline-flex"
          gap={1}
          userSelect="none"
          fontFamily="var(--vscode-font-family)"
          className="group w-full h-[26px] flex-none  cursor-pointer pl-[8px] pr-[20px] rounded hover:bg-list-hoverBackground"
          onClick={() => {
            kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(node.structure);
          }}
          position="relative"
        >
          <NodeIcon node={node} />
          <Box maxW={`${140 - (rangeRefSize?.width || 0)}px`} className="w-0 flex-auto">
            {useFileComponent(type)
              ? <FilenameDisplay tooltip={false} className={clsx("w-full", unavailable && " text-disabledForeground line-through")} maxSuffixLenth={6} filename={filename} />
              : (
                  <div className=" flex items-center">
                    <AutoTooltip className={clsx("text-xs inline-block align-top", { "text-disabledForeground line-through": unavailable })} label={structureDisplayText(type, node.structure)} openDelay={700} placement="top">
                      {structureDisplayText(type, node.structure)}
                    </AutoTooltip>
                  </div>
                )}
          </Box>
          {node.structure.type === "selection" && (
            <span ref={rangeRef} className=" flex-none  text-[12px]  text-descriptionForeground">
              {displayLineRange(node.structure.range)}
            </span>
          )}
          <KidIcon onClick={onCloseClick} config={IconClose} className="absolute right-[6px] group-hover:opacity-100 opacity-0 size-[14px] text-icon-foreground" />
        </Flex>
      </Tooltip>
    </ConfigProvider>
  );
}

export function MentionNodeLabelReadonly({ structure }: {
  structure: MentionNodeV2Structure;
}) {
  const { relativePath, type } = structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  // 判断是否为终端选择节点，渲染对应图标
  const isTerminal = structure.type === "selection" && structure.uri === "terminal://current";
  const maxSuffixLength = structure.type === "selection" ? (Math.max(15 - displayLineRange(structure.range).length, 0)) : 15;

  return (
    <Tooltip label={relativePath} openDelay={700} placement="top">
      <Flex
        align="center"
        display="inline-flex"
        gap={1}
        userSelect="none"
        fontFamily="var(--vscode-font-family)"
        className="group max-w-[160px] h-[22px] flex-none  cursor-pointer  pl-[4px] pr-[6px] border border-solid border-commandCenter-inactiveBorder rounded hover:bg-toolbar-hoverBackground"
        onClick={() => {
          kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(structure);
        }}
      >
        <div className="flex-none size-[14px] flex items-center">
          {/* eslint-disable-next-line style/multiline-ternary */}
          {isTerminal ? (
            // 终端选择节点，渲染终端图标
            <IconTerminal className="size-[14px]" />
          ) : (
            <Icon icon={getMentionStructureIcon(structure)} className=" size-[14px]" />
          )}
        </div>
        <Box maxW={`${130 - (rangeRefSize?.width || 0)}px`}>
          {
            useFileComponent(type)
              ? <FilenameDisplay tooltip={false} filename={filename} maxSuffixLenth={maxSuffixLength} />
              : (
                  <div className=" flex items-center">
                    <AutoTooltip className={clsx("text-xs text-foreground inline-block align-top")} label={structureDisplayText(type, structure)} openDelay={700} placement="top">
                      {structureDisplayText(type, structure)}
                    </AutoTooltip>
                  </div>
                )
          }
        </Box>

        {structure.type === "selection" && (
          <span ref={rangeRef} className=" flex-none  text-[12px]  text-[var(--custom-text-common)]">
            {displayLineRange(structure.range)}
          </span>
        )}
      </Flex>
    </Tooltip>
  );
}

export function MentionNodeLabelReadonly2({ structure }: {
  structure: MentionNodeV2Structure;
}) {
  const { relativePath, type } = structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  // 判断是否为终端选择节点，渲染对应图标
  const isTerminal = structure.type === "selection" && structure.uri === "terminal://current";

  return (
    <ConfigProvider
      theme={{
        components: {
          Tooltip: {
            zIndexPopup: 10000/* > var(--chakra-zIndices-popover) */,
          },
        },
      }}
    >
      <Tooltip label={relativePath} openDelay={700} closeOnScroll placement="top">
        <Flex
          align="center"
          display="inline-flex"
          gap={1}
          userSelect="none"
          fontFamily="var(--vscode-font-family)"
          className="group w-full h-[26px] flex-none  cursor-pointer px-[8px] rounded hover:bg-toolbar-hoverBackground"
          onClick={() => {
            kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(structure);
          }}
        >
          {/* eslint-disable-next-line style/multiline-ternary */}
          {isTerminal ? (
            // 终端选择节点，渲染终端图标
            <IconTerminal className="flex-none size-[14px]" />
          ) : (
            <Icon icon={getMentionStructureIcon(structure)} className=" flex-none size-[14px]" />
          )}
          <Box maxW={`${140 - (rangeRefSize?.width || 0)}px`}>
            {
              useFileComponent(type)
                ? <FilenameDisplay tooltip={false} className=" w-full" maxSuffixLenth={6} filename={filename} />
                : (
                    <div className=" flex items-center">
                      <AutoTooltip className={clsx("text-xs inline-block align-top")} label={structureDisplayText(type, structure)} openDelay={700} placement="top">
                        {structureDisplayText(type, structure)}
                      </AutoTooltip>
                    </div>
                  )
            }
          </Box>
          {structure.type === "selection" && (
            <span ref={rangeRef} className=" flex-none  text-[12px] text-foreground">
              {displayLineRange(structure.range)}
            </span>
          )}
        </Flex>
      </Tooltip>
    </ConfigProvider>
  );
}
