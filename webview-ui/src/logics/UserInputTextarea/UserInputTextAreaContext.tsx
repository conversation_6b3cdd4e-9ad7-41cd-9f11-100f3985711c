import { createContext, useContext } from "react";
import { UserInputTextareaProps } from "./UserInputTextArea";
import { ContextHeaderState } from "./ContextHeader/ContextHeaderContext";

export const UserInputTextareaContext = createContext<{
  role: UserInputTextareaProps["role"];
  contextHeaderState: ContextHeaderState;
} | null>(null);

export function useUserInputTextarea() {
  const context = useContext(UserInputTextareaContext);
  if (!context) {
    throw new Error("UserInputTextareaContext is not found");
  }
  return context;
}
