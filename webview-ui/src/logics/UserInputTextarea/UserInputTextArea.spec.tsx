import { render, waitFor } from "@testing-library/react";
import { describe } from "node:test";
import { UserInputTextarea } from "./UserInputTextArea";
import { expect, it } from "vitest";
import { TestContext } from "@/tests/TestContext";
import { mockObservableAPI } from "@/bridge/testKwaipilotBridgeAPI";
import { ReplaySubject, shareReplay } from "rxjs";
import { SelectionOrFileContext } from "shared/lib/bridge/protocol-observable";
import { ContextHeaderItem, useContextHeaderState } from "./ContextHeader/ContextHeaderContext";
import eventBus from "@/utils/eventBus";
import { createPlainTextEditorState } from "@/components/TextArea/lexical/editorState";

describe("过往缺陷 case", () => {
  it("回退时不会出现多个节点 https://team.corp.kuaishou.com/task/B2552204", async () => {
    const currentFileAndSelectionSubject = new ReplaySubject<SelectionOrFileContext | null>();
    const currentFileAndSelectionObservable = currentFileAndSelectionSubject.pipe(shareReplay(2));
    currentFileAndSelectionSubject.next({
      uri: "file://fake/file.ts",
      relativePath: "fake/file.ts",
      content: "",
    });

    mockObservableAPI("currentFileAndSelection", () => {
      return currentFileAndSelectionObservable;
    });

    const contextHeaderStateRef: {
      current: ReturnType<typeof useContextHeaderState> | null;
    } = {
      current: null,
    };

    function TestWrapper() {
      const contextHeaderState = useContextHeaderState({
        initialNodes: [],
        sessionId: "",
      });
      contextHeaderStateRef.current = contextHeaderState;
      return (
        <TestContext>
          <UserInputTextarea.RootProvider value={{
            contextHeaderState,
            role: "bottom",
          }}
          >
            <UserInputTextarea
              role="bottom"
              doSubmit={() => Promise.resolve({ result: true })}
              doStop={() => {}}
              isStreaming={false}
              isContextConsumer={true}
              sessionId=""
              placeholder={<div>Placeholder</div>}
              mode="composer"
            >
            </UserInputTextarea>
          </UserInputTextarea.RootProvider>
        </TestContext>
      );
    }

    render(
      <TestWrapper />,
    );
    await waitFor(() => {
      expect(contextHeaderStateRef.current?.nodes).toEqual([{
        structure: {
          type: "file",
          uri: "file://fake/file.ts",
          relativePath: "fake/file.ts",
        },
        followActiveEditor: true,
        isVirtualContext: false,
      } satisfies ContextHeaderItem]);
    });

    eventBus.emit("composer:onResetCheckpoint", {
      humanMessage: {
        sessionId: "",
        ts: 1,
        type: "say",
        editorState: createPlainTextEditorState(""),
        contextItems: [{
          type: "file",
          uri: "file://changed/file.ts",
          relativePath: "changed/file.ts",
        }],
        diagnostics: [],
        role: "user",
      },
    });
    const expectedNode: ContextHeaderItem = {
      structure: {
        type: "file",
        uri: "file://changed/file.ts",
        relativePath: "changed/file.ts",
      },
      followActiveEditor: false,
      isVirtualContext: false,
    };
    await waitFor(() => {
      expect(contextHeaderStateRef.current?.nodes).toEqual([expectedNode]);
    });
    await new Promise(resolve => setTimeout(resolve, 500)); // 等待下一帧
    // 需要保证后续不会被覆盖;
    expect(contextHeaderStateRef.current?.nodes).toEqual([expectedNode]);
  });
});
