import { useCallback, useMemo, useState, useEffect, useRef } from "react";
import {
  ConversationStructure,
  PaginationState,
  INITIAL_PAGINATION_STATE,
  DEFAULT_MESSAGE_PAGINATION_CONFIG,
} from "../types";

interface UseMessagePaginationResult {
  /** 当前显示的对话列表 */
  visibleConversations: ConversationStructure[];
  /** 分页状态 */
  paginationState: PaginationState;
  /** 加载更多历史消息 */
  loadMoreHistory: () => void;
  /** 重置分页状态（发送新消息时调用） */
  resetPagination: () => void;
}

/**
 * 消息分页 Hook
 * @param conversations 所有对话列表
 * @returns 分页相关状态和方法
 */
export function useMessagePagination(
  conversations: ConversationStructure[],
): UseMessagePaginationResult {
  const [paginationState, setPaginationState] = useState<PaginationState>(INITIAL_PAGINATION_STATE);
  const prevConversationCount = useRef(conversations.length);

  // 计算是否有更多历史消息
  const hasMoreHistory = useMemo(() => {
    if (paginationState.showAllMessages) {
      return false;
    }
    return conversations.length > paginationState.displayCount;
  }, [conversations.length, paginationState.displayCount, paginationState.showAllMessages]);

  // 更新分页状态中的 hasMoreHistory
  useEffect(() => {
    setPaginationState(prev => ({
      ...prev,
      hasMoreHistory,
    }));
  }, [hasMoreHistory]);

  // 监听对话数量变化，如果有新对话且当前显示所有消息，自动重置为默认状态
  useEffect(() => {
    const currentCount = conversations.length;
    const prevCount = prevConversationCount.current;

    // 如果有新对话添加且当前显示所有消息，重置分页
    if (currentCount > prevCount && paginationState.showAllMessages) {
      setPaginationState({
        ...INITIAL_PAGINATION_STATE,
        hasMoreHistory: currentCount > DEFAULT_MESSAGE_PAGINATION_CONFIG.defaultDisplayCount,
      });
    }

    prevConversationCount.current = currentCount;
  }, [conversations.length, paginationState.showAllMessages]);

  // 计算当前显示的对话列表（取最新的几条）
  const visibleConversations = useMemo(() => {
    if (paginationState.showAllMessages) {
      return conversations;
    }

    // 返回最新的 displayCount 条对话
    const startIndex = Math.max(0, conversations.length - paginationState.displayCount);
    return conversations.slice(startIndex);
  }, [conversations, paginationState.displayCount, paginationState.showAllMessages]);

  // 加载更多历史消息
  const loadMoreHistory = useCallback(() => {
    if (paginationState.isLoadingHistory || !hasMoreHistory) {
      return;
    }

    setPaginationState(prev => ({
      ...prev,
      isLoadingHistory: true,
    }));

    setPaginationState((prev) => {
      const newDisplayCount = prev.displayCount + DEFAULT_MESSAGE_PAGINATION_CONFIG.loadMoreCount;
      const showAllMessages = newDisplayCount >= conversations.length;

      return {
        ...prev,
        displayCount: showAllMessages ? conversations.length : newDisplayCount,
        showAllMessages,
        isLoadingHistory: false,
        hasMoreHistory: !showAllMessages,
      };
    });
  }, [paginationState.isLoadingHistory, hasMoreHistory, conversations.length]);

  // 重置分页状态（发送新消息时调用）
  const resetPagination = useCallback(() => {
    setPaginationState({
      ...INITIAL_PAGINATION_STATE,
      hasMoreHistory: conversations.length > DEFAULT_MESSAGE_PAGINATION_CONFIG.defaultDisplayCount,
    });
  }, [conversations.length]);

  return {
    visibleConversations,
    paginationState,
    loadMoreHistory,
    resetPagination,
  };
}
