import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spinner } from "@chakra-ui/react";
import { Tooltip } from "@/components/Union/chakra-ui";

import { useCallback, useEffect, useState } from "react";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { COMMAND_OUTPUT_STRING, COMMAND_REQ_APP_STRING, TerminalTextStructure } from "shared/lib/agent";
import { isTerminalMessage } from "shared/lib/agent/isToolMessage";

import { useComposerState } from "../context/ComposerStateContext";
import clsx from "clsx";
import { vsCss } from "shared/lib/vscodeToken/index";
import AutoTooltip from "@/components/AutoTooltip";
import { AutoRunSelect } from "./components/AutoRunSelect";
import { t } from "i18next";
import TerminalBlock from "../components/TerminalBlock";
import { EditableTerminal } from "@/components/EditableTerminal";
import { IconLineExternal, IconTerminal } from "@/components/Icons";
import { terminal } from "@/components/Terminal";

function IconPendingDot() {
  return (
    <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle opacity="0.5" cx="4.78174" cy="4.23468" r="3" fill="#FFBB26" />
      <circle cx="4.78174" cy="4.23468" r="2" fill="#FFBB26" />
    </svg>
  );
}

const splitMessage = (text: string) => {
  const outputIndex = text.indexOf(COMMAND_OUTPUT_STRING);
  if (outputIndex === -1) {
    return { command: text, output: "" };
  }
  return {
    command: text.slice(0, outputIndex).trim(),
    output: text
      .slice(outputIndex + COMMAND_OUTPUT_STRING.length)
      .trim()
      .split("")
      .map((char) => {
        switch (char) {
          case "\t":
            return "→   ";
          case "\b":
            return "⌫";
          case "\f":
            return "⏏";
          case "\v":
            return "⇳";
          default:
            return char;
        }
      })
      .join(""),
  };
};

function TerminalFooter({
  handleEnableChange,
}: {
  handleEnableChange?: (enabled: boolean) => void;
}) {
  const { message } = useComposerTaskContext();
  if (!isTerminalMessage(message)) {
    throw new Error("message is not a terminal message");
  }

  const autoRunFailedTips = message.autoRunFailedReason
    ? message.autoRunFailedReason === "agentRequiredApproval"
      ? (
          <AutoTooltip label={t("terminal.riskCommand")} className=" text-disabledForeground text-[10px]">
            {t("terminal.riskCommand")}
          </AutoTooltip>
        )
      : message.autoRunFailedReason === "excluded"
        ? (
            <AutoTooltip label={t("terminal.blacklistCommand")} className=" text-disabledForeground text-[10px]">

              {t("terminal.blacklistPrefix")}
              <Button
                variant="link"
                textDecoration="none"
                size="10px"
                color={vsCss.textLinkForeground}
                _active={{ color: vsCss.textLinkForeground }}
                mx={1}
                onClick={() => kwaiPilotBridgeAPI.extensionSettings.$openSettings("function")}
              >

                {t("terminal.blockListButton")}
              </Button>
              {t("terminal.blacklistSuffix")}
            </AutoTooltip>
          )
        : undefined
    : undefined;

  return (
    <div className="bg-editor-background rounded-bl-lg rounded-br-lg p-1.5 flex items-center gap-2">
      {autoRunFailedTips}
      <AutoRunSelect handleEnableChange={handleEnableChange} />
    </div>
  );
}

export function Terminal() {
  const { message, isLast } = useComposerTaskContext();
  const { currentTaskInterrupted } = useComposerState();

  const [commandRunClicked, setCommandRunClicked] = useState(false);
  const [commandCancelClicked, setCommandCancelClicked] = useState(false);

  if (!isTerminalMessage(message)) {
    throw new Error("message is not a terminal message");
  }
  const terminalInfo = JSON.parse(message.text || "{}") as TerminalTextStructure;
  const { command: rawCommand, output } = splitMessage(terminalInfo.command || "");
  const requestsApproval = rawCommand.endsWith(COMMAND_REQ_APP_STRING);
  const command = requestsApproval ? rawCommand.slice(0, -COMMAND_REQ_APP_STRING.length) : rawCommand;

  // 添加状态来保存用户编辑后的命令内容
  const [editedCommand, setEditedCommand] = useState<string>("");
  const [hasBeenEdited, setHasBeenEdited] = useState<boolean>(false);

  // 使用编辑后的命令或原始命令
  const displayCommand = hasBeenEdited ? editedCommand : command;

  // 判断运行按钮是否应该禁用
  const isRunDisabled = hasBeenEdited ? !editedCommand.trim() : !command.trim();
  // 处理命令编辑
  const handleCommandChange = useCallback((newCommand: string) => {
    setEditedCommand(newCommand);
    setHasBeenEdited(true);
  }, []);

  /**
   * 点击运行终端
   */
  const onRunTerminalClick = useCallback(() => {
    if (isRunDisabled) {
      return;
    }
    // 发送用户编辑后的命令（如果有的话）
    const commandToRun = hasBeenEdited ? editedCommand : command;
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "yesButtonClicked",
      text: commandToRun,
    });
    setCommandRunClicked(true);
  }, [isRunDisabled, hasBeenEdited, editedCommand, command]);
  const onSkipClick = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "noButtonClicked",
      text: "",
    });
    setCommandCancelClicked(true);
  }, []);

  const onOpenTerminalClick = useCallback(() => {
    terminal.showTerminal();
  }, []);

  // const outputReceived = useMemo(() => message?.text?.includes(COMMAND_OUTPUT_STRING), [message]);
  const isCommandExecuting = ("outputMessage" in message && message.outputMessage?.partial) && isLast && commandRunClicked;
  const canCommandProceededByUser = !currentTaskInterrupted && !commandRunClicked && !commandCancelClicked;

  useEffect(() => {
    if (canCommandProceededByUser && message.autoRun && !isRunDisabled) {
      onRunTerminalClick();
    }
  }, [canCommandProceededByUser, message.autoRun, onRunTerminalClick, isRunDisabled]);

  const handleEnableChange = useCallback((enabled: boolean) => {
    if (enabled && canCommandProceededByUser && !isRunDisabled) {
      // 自动执行
      onRunTerminalClick();
    }
  }, [canCommandProceededByUser, onRunTerminalClick, isRunDisabled]);

  return (
    <div className={clsx(" rounded-lg border-[0.6px]  border-commandCenter-activeBorder")}>
      <div className=" flex rounded-tl-lg rounded-tr-lg items-center gap-1 leading-[18px] text-[13px] py-1 px-1.5 bg-sideBar-background">
        {(message.partial || isCommandExecuting) && <Spinner size="xs" color="inherit" />}
        <IconTerminal className="size-3 text-icon-foreground" />
        <Show breakpoint="(min-width: 320px)">
          <span className="text-[13px] text-foreground">
            {t("terminal.commandLine")}
          </span>
        </Show>
        {
          !message.partial && isLast && !currentTaskInterrupted
          && !commandRunClicked && !commandCancelClicked && (
            <Tooltip label={t("chat.waitingForOperation")} placement="top">
              <div className=" ml-1 whitespace-nowrap">
                <span className=" text-disabledForeground text-[10px] flex items-center gap-1">
                  <IconPendingDot />
                  <span>{t("terminal.commandLinePlaceholder")}</span>
                </span>
              </div>
            </Tooltip>
          )
        }

        {!message.partial && isLast && (
          <Flex gap={2} ml="auto" align="center" flex="none">
            <Tooltip label={t("terminal.openTerminal")} placement="top">
              <IconLineExternal onClick={onOpenTerminalClick} className="size-3 text-disabledForeground cursor-pointer hover:text-foreground" />
            </Tooltip>
            {canCommandProceededByUser && (
              <>
                <div className="h-[12px] w-[1px] bg-commandCenter-inactiveBorder rounded-sm"></div>
                <div className="text-[10px] px-1 py-[1px] rounded-[4px] text-disabledForeground cursor-pointer hover:bg-button-secondaryHoverBackground" onClick={onSkipClick}>
                  {t("common.cancel_action")}
                </div>
                <div
                  style={
                    isRunDisabled
                      ? { cursor: "not-allowed", opacity: 0.5, background: vsCss.buttonSecondaryBackground }
                      : {}
                  }
                  onClick={onRunTerminalClick}
                  className="text-[10px] px-1 py-[1px] bg-button-background rounded-[4px] text-foreground cursor-pointer hover:bg-button-hoverBackground"
                >
                  {t("common.run")}
                </div>
              </>
            ) }
          </Flex>
        )}
      </div>
      <TerminalBlock
        language="shellscript"
        source={displayCommand}
        forceWrap={true}
        clearPrebg={true}
        className="w-max"
        canEdit={!message.partial && isLast && canCommandProceededByUser}
        onSourceChange={handleCommandChange}
      />
      <EditableTerminal output={output} commandCancelClicked={commandCancelClicked} />
      <TerminalFooter handleEnableChange={handleEnableChange} />
    </div>
  );
}
