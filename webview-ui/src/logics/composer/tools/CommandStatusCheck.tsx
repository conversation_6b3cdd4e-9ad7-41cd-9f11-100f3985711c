import { isCommandStatusCheckMessage } from "shared/lib/agent/isToolMessage";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { CommandStatusCheckStructure, COMMAND_STATUS_CHECK_RESULT_STRING } from "shared/lib/agent";
import { useCallback, useMemo } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Icon } from "@/components/Union/t-iconify";
import { IconTerminal } from "@/components/Icons";
import { Spinner } from "@chakra-ui/react";
import { reportUserAction } from "@/utils/weblogger";

const splitCommandStatusCheckMessage = (text: string) => {
  try {
    const parsedContent = JSON.parse(text || "{}") as CommandStatusCheckStructure & {
      combined_result_marker?: string;
      result?: string;
    };

    // 如果有合并的结果标记，说明是合并后的消息
    if (parsedContent.combined_result_marker === COMMAND_STATUS_CHECK_RESULT_STRING) {
      return {
        checkInfo: {
          check_duration: parsedContent.check_duration,
        } as CommandStatusCheckStructure,
        result: parsedContent.result || "",
      };
    }

    // 如果没有合并标记，返回原始数据
    return {
      checkInfo: parsedContent as CommandStatusCheckStructure,
      result: "",
    };
  }
  catch (e) {
    // 解析失败，返回默认值
    return {
      checkInfo: { check_duration: 0 } as CommandStatusCheckStructure,
      result: "",
    };
  }
};
const parseResult = (text: string) => {
  try {
    return JSON.parse(text || "{}") as {
      status: string;
      output: string;
    };
  }
  catch (e) {
    return {
      status: "error",
      output: "",
    };
  }
};

function IconDot({ className }: { className?: string }) {
  return (
    <svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <circle cx="4" cy="4.70728" r="2" fill="currentColor" />
    </svg>
  );
}

export function CommandStatusCheck() {
  const { message } = useComposerTaskContext();
  console.log("check_duration:", message);
  if (!isCommandStatusCheckMessage(message)) {
    throw new Error("message is not a command status check message");
  }

  const { checkInfo: CommandStatusCheckInfo, result } = splitCommandStatusCheckMessage(message.text || "{}");
  const { status } = parseResult(result);

  const skipCheck = useCallback(() => {
    reportUserAction({
      key: "command_skip",
      type: undefined,
      content: JSON.stringify({
        command_result: result,
      }),
    });
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "noButtonClicked",
      text: "",
    });
  }, [result]);

  const toolStatus = (status === "success" || status == "error") ? "success" : status === "skip" ? "skiped" : "running";
  const data = useMemo(() => {
    if (toolStatus === "success") {
      return {
        prefix: <IconTerminal className="size-[12px]" />,
        text: "命令行检测",
        icon: <IconDot className="text-[#27C241] flex-none" />,
        opt: "",
      };
    }
    else if (toolStatus === "skiped") {
      return {
        prefix: <Icon icon="codicon:terminal" />,
        text: "命令行检测",
        icon: (
          <div className="text-disabledForeground text-[10px]">
            已取消
          </div>
        ),
        opt: "",
      };
    }
    else {
      return {
        prefix: <Spinner size="xs" color="inherit" />,
        text: "命令行检测",
        icon: (
          <div className="text-disabledForeground text-[10px]">
            预计耗时
            {CommandStatusCheckInfo.check_duration}
            {" "}
            s
          </div>
        ),
        opt: <div onClick={skipCheck} className=" cursor-pointer ">取消</div>,
      };
    }
  }, [toolStatus, skipCheck, CommandStatusCheckInfo.check_duration]);

  return (
    <div className="width-full bg-sideBar-background rounded-lg border-[0.6px] border-commandCenter-activeBorder flex items-center justify-between h-[26px] px-3">
      <div className="flex items-center justify-center gap-1">
        {data.prefix}
        {data.text}
        {data.icon}
      </div>
      <div>
        {data.opt}
      </div>
    </div>
  );
}
