import { kwaiPilotBridgeAPI } from "@/bridge";
import { <PERSON><PERSON>, Spinner, Textarea, Box } from "@chakra-ui/react";
import { useCallback, useMemo, useState } from "react";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { useComposerState } from "../context/ComposerStateContext";
import { ComposerMarkdownRender } from "../components/ComposerMarkdownRender";
import clsx from "clsx";
import { t } from "i18next";

export function UserInput() {
  const { message, isLast } = useComposerTaskContext();
  const { currentTaskInterrupted } = useComposerState();
  const [mcpRunClicked, setMcpRunClicked] = useState(false);
  const [input, setInput] = useState("");

  const jsonMsg = JSON.parse(message.text || "{}") as { query: string; response: string; error: string };
  const { query } = jsonMsg;
  const resultReceived = useMemo(
    () => jsonMsg.response || jsonMsg.error,
    [jsonMsg.error, jsonMsg.response],
  );
  const isCommandExecuting = !resultReceived && isLast && mcpRunClicked;
  const isWaitingForExcute
    = !currentTaskInterrupted && !mcpRunClicked;

  /**
   * 点击运行终端
   */
  const onRunMcpClick = useCallback(async () => {
    setMcpRunClicked(true);
    await kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "yesButtonClicked",
      text: input,
    });
  }, [input]);

  const handleCopy = useCallback((content: string) => {
    kwaiPilotBridgeAPI.copyToClipboard(content.replace(/\n$/, ""));
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: t("common.copied"),
    });
  }, []);

  return (
    <div
      className={clsx(
        "border border-solid border-radio-inactiveBorder rounded-[8px] my-2",
      )}
    >
      <div
        className={clsx(
          "flex items-center gap-1 leading-[18px] text-[13px] px-[12px] py-[8px] border-none cursor-pointer bg-statusBarItem-remoteHoverBackground",
        )}
      >
        <ComposerMarkdownRender content={query} handleCopy={handleCopy} />
      </div>
      <div
        className={clsx(
          "transition-all duration-200 ease-in-out overflow-auto relative",
        )}
      >
        {
          resultReceived
            ? <Box p={3}>{jsonMsg.response}</Box>
            : <Textarea value={input} size="sm" placeholder={t("mention.inputPlaceholder")} onChange={e => setInput(e.target.value)} className="w-full min-h-[20px] text-xs leading-5" />
        }
        {
          !resultReceived && (
            <Box position="absolute" bottom="12px" right="12px">
              {!message.partial && isLast && (
                <Flex gap={2} ml="auto" align="center" flex="none">
                  {isWaitingForExcute && (
                    <>
                      <button
                        className="text-[12px] font-medium flex items-center gap-1 text-[var(--custom-text-common)] leading-[18px] py-[2px] px-1 rounded bg-button-background hover:bg-button-hoverBackground"
                        onClick={onRunMcpClick}
                      >
                        <span>提交</span>
                      </button>
                    </>
                  )}
                  {isCommandExecuting && (
                    <button
                      className="text-[12px] font-medium flex items-center gap-1 text-[var(--custom-text-common)] leading-[18px] py-[2px] px-1 rounded bg-button-background hover:bg-button-hoverBackground"
                      onClick={onRunMcpClick}
                    >
                      <Spinner size="xs"></Spinner>
                      <span>运行</span>
                    </button>
                  )}
                </Flex>
              )}
            </Box>
          )
        }
      </div>
    </div>
  );
}
