import { t } from "i18next";

export const Error = (props: { errorMsg?: string }) => {
  return (
    <div className="text-[var(--colors-text-common-tertiary)] text-[12px] leading-[18px] truncate flex items-center gap-1">
      <div className="size-2 flex justify-center items-center">
        <div className="size-1 bg-[#ED5140] rounded-full"></div>
      </div>
      {props.errorMsg || t("error.toolCallFailed")}
    </div>
  );
};
