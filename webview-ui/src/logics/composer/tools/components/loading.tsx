import { t } from "i18next";

export const Loading = (props: {
  text?: string;
}) => {
  return (
    <>
      <div className="relative w-3 h-3">
        <div className="absolute w-3 h-3 border-2 border-[#8D96A0] rounded-full"></div>
        <svg className="absolute w-3 h-3 animate-spin" viewBox="0 0 48 48">
          <circle
            cx="24"
            cy="24"
            r="20"
            stroke="currentColor"
            stroke-width="4"
            stroke-linecap="round"
            className="text-[#E5ECF2]"
            fill="none"
            stroke-dasharray="120"
            stroke-dashoffset="25"
          />
        </svg>
      </div>
      <div className="text-text-common-tertiary text-[13px] leading-[18px]">{props.text ?? t("chat.executing")}</div>
    </>
  );
};
