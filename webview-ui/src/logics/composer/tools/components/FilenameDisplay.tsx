import { vsCss } from "@/style/vscode";
import { Typography, ConfigProvider, TooltipProps } from "antd";
import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

const highlightStyle: React.CSSProperties = {
  fontFeatureSettings: `'clig' off, 'liga' off`,
  fontWeight: "500",
  color: vsCss.menuSelectionBackground,
};
const splitTextIntoSmallChunks = (text: string, chunkSize: number) => {
  let cursor = 0;
  const chunks: string[] = [];
  while (cursor < text.length) {
    const chunk = text.slice(cursor, cursor + chunkSize);
    chunks.push(chunk);
    cursor += chunkSize;
  }
  return chunks;
};
function renderHighlightText({ text, highlight }: { text: string;highlight: string }): ReactNode[] {
  // antd ellipsis 按 element 为最小单元做 ellipsis 一段不能过长 否则会整段用...省略
  const parts = text.split(new RegExp(`(${highlight})`, "gi"))
    .map((part, i) => ({
      part,
      isHighlight: i % 2 === 1, // 奇数为高亮部分
    }));

  const minimizedParts = parts.map(part => part.isHighlight ? part : splitTextIntoSmallChunks(part.part, 5).map(v => ({ part: v, isHighlight: false }))).flat();

  return minimizedParts.map((part, index) =>
  // 不区分大小写地检查是否为高亮部分
    part.isHighlight
      ? (
          <span key={index} style={highlightStyle}>
            {part.part}
          </span>
        )
      : (
          <span key={index}>
            {part.part}
          </span>
        ),
  );
}

export interface FilenameHighlightMatch {
  start: number;
  end: number;
}

function renderSeparatedFilename({
  highlight,
  filename,
  maxSuffixLenth,
}: {
  highlight?: string;
  filename: string;
  maxSuffixLenth: number;
}) {
  const filenameSuffixI = filename.length - Math.min(Math.floor(filename.length / 2), maxSuffixLenth || 15);
  const filenamePrefixPart = filename.slice(0, filenameSuffixI);
  const filenameSuffixPart = filename.slice(filenameSuffixI);

  const suffix = highlight
    ? (renderHighlightText({
        text: filenameSuffixPart,
        highlight,
      })) as unknown as string
    : filenameSuffixPart;
  const prefix = highlight
    ? renderHighlightText({
        text: filenamePrefixPart,
        highlight: highlight,
      })
    : filenamePrefixPart;
  return {
    prefix,
    suffix,
  };
}

function renderSeparatedFilenameByMatch({
  highlightResult,
  filename,
  maxSuffixLenth,
}: {
  filename: string;
  maxSuffixLenth: number;
  highlightResult: FilenameHighlightMatch[];
}) {
  const match = highlightResult[0];

  const filenameSuffixI = filename.length - Math.min(Math.floor(filename.length / 2), maxSuffixLenth || 15);

  let prefixChunks: { part: string; isHighlight: boolean }[] = [];
  let suffixChunks: { part: string; isHighlight: boolean }[] = [];

  if (filenameSuffixI < match.start) {
    // 分割点在高亮区域之前
    prefixChunks = [
      { part: filename.slice(0, filenameSuffixI), isHighlight: false },
    ];
    suffixChunks = [
      { part: filename.slice(filenameSuffixI, match.start), isHighlight: false },
      { part: filename.slice(match.start, match.end), isHighlight: true },
      { part: filename.slice(match.end), isHighlight: false },
    ];
  }
  else if (filenameSuffixI < match.end) {
    // 分割点在高亮区域中间
    prefixChunks = [
      { part: filename.slice(0, match.start), isHighlight: false },
      { part: filename.slice(match.start, filenameSuffixI), isHighlight: true },
    ];
    suffixChunks = [
      { part: filename.slice(filenameSuffixI, match.end), isHighlight: true },
      { part: filename.slice(match.end), isHighlight: false },
    ];
  }
  else {
    // 分割点在高亮区域之后
    prefixChunks = [
      { part: filename.slice(0, match.start), isHighlight: false },
      { part: filename.slice(match.start, match.end), isHighlight: true },
      { part: filename.slice(match.end, filenameSuffixI), isHighlight: false },
    ];
    suffixChunks = [
      { part: filename.slice(filenameSuffixI), isHighlight: false },
    ];
  }

  prefixChunks = prefixChunks.map(chunk => chunk.isHighlight ? chunk : splitTextIntoSmallChunks(chunk.part, 5).map(v => ({ part: v, isHighlight: false }))).flat();
  suffixChunks = suffixChunks.map(chunk => chunk.isHighlight ? chunk : splitTextIntoSmallChunks(chunk.part, 5).map(v => ({ part: v, isHighlight: false }))).flat();

  return {
    prefix: prefixChunks.map((chunk, index) =>
      chunk.isHighlight
        ? (
            <span data-highlight key={index} style={highlightStyle}>
              {chunk.part}
            </span>
          )
        : (
            <span key={index}>
              {chunk.part}
            </span>
          ),
    ),
    suffix: suffixChunks.map((chunk, index) =>
      chunk.isHighlight
        ? (
            <span data-highlight key={index} style={highlightStyle}>
              {chunk.part}
            </span>
          )
        : (
            <span key={index}>
              {chunk.part}
            </span>
          ),
    ),
  };
}

/**
 * 展示文件名，主要是中间截断
 */
export function FilenameDisplay({
  filename,
  className,
  maxSuffixLenth,
  tooltip,
  highlight,
  highlightResult,
}: {
  filename: string;
  className?: string;
  maxSuffixLenth?: number;
  tooltip?: React.ReactNode | TooltipProps;
  highlight?: string;
  highlightResult?: FilenameHighlightMatch[];
}) {
  const { prefix, suffix } = highlightResult?.length
    ? renderSeparatedFilenameByMatch({
        highlightResult,
        filename,
        maxSuffixLenth: maxSuffixLenth || 15,
      })
    : renderSeparatedFilename({
        highlight,
        filename,
        maxSuffixLenth: maxSuffixLenth || 15,
      });

  return (
    <ConfigProvider theme={{
      token: {
        colorBgSpotlight: vsCss.editorHoverWidgetBackground,
        colorText: vsCss.editorHoverWidgetForeground,
        colorTextLightSolid: vsCss.editorHoverWidgetForeground,
        fontFamily: "var(--vscode-font-family)",
      },
      cssVar: true,
    }}
    >

      <Typography.Text
        ellipsis={{
          tooltip: tooltip ?? {
            title: (
              <span className=" text-[12px] leading-[18px]">
                {filename}
              </span>
            ),
            styles: {
              body: {
                border: `1px solid ${vsCss.editorHoverWidgetBorder}`,
              },
            },
            arrow: false,
          },
          suffix: suffix as any,
        }}
        className={twMerge(" flex-auto text-[12px] text-foreground whitespace-nowrap", className)}
      >
        {prefix}
      </Typography.Text>
    </ConfigProvider>
  );
}
