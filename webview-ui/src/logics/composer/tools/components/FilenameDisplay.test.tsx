import { render } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { FilenameDisplay } from "./FilenameDisplay";

describe("FilenameDisplay", () => {
  it("renderResult", () => {
    const { container } = render(
      /* config -> co*nf*ig */
      <FilenameDisplay
        filename="config"
        highlightResult={[{
          start: 2,
          end: 4,
        }]}
      />,
    );

    const elms = [...container.querySelectorAll("span[data-highlight]")];
    expect(elms.map(v => v.innerHTML).join("")).toBe("nf");
  });
});
