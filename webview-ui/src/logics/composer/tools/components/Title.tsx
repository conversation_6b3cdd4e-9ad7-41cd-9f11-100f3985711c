import { useMemo } from "react";
import { t } from "i18next";

interface IProps {
  type: string;
}

export const Title = (props: IProps) => {
  const { type } = props;

  const title = useMemo(() => {
    switch (type) {
      case "saveMemory":
        return t("tool.saveMemory");
      case "searchMemory":
        return t("tool.searchMemory");
      case "readTask":
        return t("tool.readTask");
      case "readFile":
        return t("tool.fileView");
      case "codebaseSearch":
        return t("tool.codeSearch");
      case "listFilesTopLevel":
        return t("tool.fileList");
      case "listFilesRecursive":
        return t("tool.fileList");
      case "grepSearch":
        return t("tool.regexSearch");
    }
  }, [type]);

  return <div className="text-foreground leading-[18px] text-[13px] whitespace-nowrap align-middle">{title}</div>;
};
