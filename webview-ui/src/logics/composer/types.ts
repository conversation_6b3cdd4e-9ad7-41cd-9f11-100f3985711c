import { InternalLocalMessage } from "shared/lib/agent";

/**
 * 对话结构定义
 */
export interface ConversationStructure {
  humanMessage: InternalLocalMessage;
  taskRows: InternalLocalMessage[];
}

/**
 * 消息分页配置
 */
export interface MessagePaginationConfig {
  /** 默认显示的对话条数 */
  defaultDisplayCount: number;
  /** 每次加载更多的对话条数 */
  loadMoreCount: number;
}

/**
 * 分页状态
 */
export interface PaginationState {
  /** 当前显示的对话数量 */
  displayCount: number;
  /** 是否有更多历史对话可以加载 */
  hasMoreHistory: boolean;
  /** 是否正在加载历史对话 */
  isLoadingHistory: boolean;
  /** 是否显示所有对话 */
  showAllMessages: boolean;
}

/**
 * 默认分页配置
 */
export const DEFAULT_MESSAGE_PAGINATION_CONFIG: MessagePaginationConfig = {
  defaultDisplayCount: 10, // 默认显示 10 条对话
  loadMoreCount: 5, // 每次加载 5 条对话
};

/**
 * 初始分页状态
 */
export const INITIAL_PAGINATION_STATE: PaginationState = {
  displayCount: DEFAULT_MESSAGE_PAGINATION_CONFIG.defaultDisplayCount,
  hasMoreHistory: false,
  isLoadingHistory: false,
  showAllMessages: false,
};
