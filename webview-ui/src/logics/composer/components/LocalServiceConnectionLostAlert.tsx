import AutoTooltip from "@/components/AutoTooltip";
import {
  Alert,
  AlertIcon,
  Box,
  BoxProps,
  CloseButton,
  useDisclosure,
} from "@chakra-ui/react";
import { t } from "i18next";

export function LocalServiceConnectionLostAlert(props: BoxProps) {
  const { isOpen: isVisible, onClose } = useDisclosure({ defaultIsOpen: true });
  if (!isVisible) {
    return null;
  }
  return (
    <Box {...props}>
      <Alert w="full" height="36px" status="error" alignItems="center">
        <AlertIcon boxSize="4" />
        <AutoTooltip
          label={t("localService.connectionLost")}
          className=" text-[12px] font-medium"
        >
          {t("localService.connectionLost")}
        </AutoTooltip>
        <CloseButton
          alignSelf="flex-start"
          onClick={onClose}
          ml="auto"
          css={{
            alignSelf: "center",
          }}
        />
      </Alert>
    </Box>
  );
}
