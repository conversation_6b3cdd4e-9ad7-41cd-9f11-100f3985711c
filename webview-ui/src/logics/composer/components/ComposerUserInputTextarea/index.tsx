import { UserInputTextareaProps, UserInputTextarea } from "@/logics/UserInputTextarea/UserInputTextArea";
import { UploadImageBtn } from "./UploadImageBtn";
import { useComposerModelSupport } from "@/hooks/useComposerModelSupport";
import { t } from "i18next";

export const ComposerUserInputTextarea: React.FC<Omit<UserInputTextareaProps, "placeholder" | "mode">> = (props) => {
  const { allowImage } = useComposerModelSupport();

  return (
    <UserInputTextarea.Root
      role={props.role}
      localMessage={props.localMessage}
      sessionId={props.sessionId}
      contextStorageService={props.contextStorageService}
    >
      <UserInputTextarea
        placeholder={(
          <div className="absolute text-[13px] leading-[19.5px] text-tab-inactiveForeground top-2 left-3">
            {t("chat.placeholder1")}
          </div>
        )}
        action={allowImage
          ? (
              <UploadImageBtn />
            )
          : undefined}
        mode="composer"
        allowImage={allowImage}
        {...props}
      >
      </UserInputTextarea>
    </UserInputTextarea.Root>
  );
};
