import { kwaiPilotBridgeAPI } from "@/bridge";
import { getIcon } from "@/utils/fileIcon";

import CopyIcon from "@/assets/copy.svg?react";
import ReApplyIcon from "@/assets/codeblock/renew.svg?react";
import DiffIcon from "@/assets/codeblock/diff.svg?react";
import CodeIcon from "@/assets/codeblock/code.svg?react";
import ComposerAcceptIcon from "@/assets/codeblock/accept.svg?react";
import ComposerRejectIcon from "@/assets/codeblock/reject.svg?react";
import ComposerDotIcon from "@/assets/codeblock/dot.svg?react";
import { Icon } from "@/components/Union/t-iconify";
import EditIcon from "@/assets/tools/tool-edit.svg?react";
import { SingleIcon } from "@/components/SingleIcon";
import AutoTooltip from "@/components/AutoTooltip";
import { Loading } from "../tools/components/loading";
import { Error } from "../tools/components/Error";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { useMemo } from "react";
import { useComposerState } from "../context/ComposerStateContext";
import { t } from "i18next";

type CodeHeaderBarProps = {
  filepath?: string;
  language: string;
  isNewTool: boolean;
  handleCopy: () => void;
  handleUndo: () => void;
  handleKeep: () => void;
  handleReapply: () => void;
  codeLoading: boolean;
  // version: number;
  // totalVersion: number;
  addLines?: number;
  delLines?: number;
  isToolError: boolean;
  showDiffView: boolean;
  handleToggleDiffView: () => void;
};

export const CodeHeaderBar = (props: CodeHeaderBarProps) => {
  const {
    isNewTool,
    filepath,
    language,
    codeLoading,
    handleKeep,
    handleCopy,
    handleReapply,
    handleUndo,
    isToolError,
    showDiffView,
    handleToggleDiffView,
    addLines,
    delLines,
  } = props;
  const { workingSetFileUIState } = useComposerTaskContext();
  const { isCurrentWorkspaceSession } = useComposerState();
  const applyState = workingSetFileUIState || "init";
  const fileName = filepath ? filepath.split("/").pop() : language;
  const iconType = getIcon(fileName ?? "", false);
  const isApplying = useMemo(
    () => applyState === "applying" || applyState === "init",
    [applyState],
  );
  const noModifyed = useMemo(
    () => addLines === 0 && delLines === 0,
    [addLines, delLines],
  );
  const DiffIconType = showDiffView ? DiffIcon : CodeIcon;

  return (
    <div className="relative rounded-t-lg flex items-center h-[32px] bg-[var(--vscode-editor-background)] sticky top-[-13px] z-[49]">
      <div
        className="flex rounded-t-lg items-center gap-2 text-[13px] leading-[18px] size-full bg-statusBarItem-remoteHoverBackground px-[12px]"
        style={{ fontFamily: "PingFang SC" }}
      >
        {/* 文件图标和名称 */}
        <div
          className="flex items-center gap-[4px] justify-center cursor-pointer text-foreground"
          onClick={() =>
            filepath
            && kwaiPilotBridgeAPI.editor.openFileToEditorMaybeDiffEditor(filepath)}
        >
          <EditIcon className="flex-shrink-0 translate-y-[0.8px] size-[14px] text-icon-foreground border rounded-[3.5px] border-commandCenter-inactiveBorder" />
          <Icon icon={iconType} className="size-[14px]" />
          <AutoTooltip className="flex-1" title={fileName} lineClamp={1}>
            {fileName}
          </AutoTooltip>
          {/* 加减行数 */}
          {applyState && !isApplying
            ? (
                noModifyed
                  ? (
                      <span className="inline-flex items-center ml-[4px] text-text-common-tertiary">
                        {t("code.noChanges")}
                      </span>
                    )
                  : (
                      addLines !== undefined
                      && delLines !== undefined && (
                        <div className="inline-flex items-center">
                          <span className="inline-flex items-center ml-[4px] text-charts-green">
                            +
                            {addLines}
                          </span>
                          <span className="inline-flex items-center ml-[4px] mr-[4px] text-charts-red">
                            -
                            {delLines}
                          </span>
                        </div>
                      )
                    )
              )
            : null}
          {/* 加载状态指示器 */}
          {isToolError
            ? (
                <Error errorMsg={t("error.fileEditFailed")} />
              )
            : codeLoading
              ? (
                  <div className="flex items-center justify-center">
                    <Loading text={t("code.generating")} />
                  </div>
                )
              : applyState === "applying"
                ? (
                    <div className="flex items-center justify-center">
                      <Loading text={t("code.applying")} />
                    </div>
                  )
                : applyState === "applied"
                  ? (
                      <ComposerDotIcon className="size-[8px] text-charts-green" />
                    )
                  : applyState === "accepted"
                    ? (
                        <ComposerAcceptIcon className="size-[14px] text-charts-green" />
                      )
                    : applyState === "rejected"
                      ? (
                          <ComposerRejectIcon className="size-[14px] text-charts-red" />
                        )
                      : (
                          <span></span>
                        )}
        </div>

        {/* 工具按钮 */}
        {!codeLoading && (
          <div className="flex items-center ml-auto text-[var(--vscode-foreground)]">
            <SingleIcon title={t("tooltip.copy")} onClick={handleCopy}>
              <CopyIcon className="size-[14px]" />
            </SingleIcon>
            <>
              {applyState === "applied"
                ? (
                    <>
                      <SingleIcon title={t("tooltip.reject")} onClick={handleUndo}>
                        <ComposerRejectIcon className="size-[14px]" />
                      </SingleIcon>
                      <SingleIcon title={t("tooltip.accept")} onClick={handleKeep}>
                        <ComposerAcceptIcon className="size-[14px]" />
                      </SingleIcon>
                    </>
                  )
                : isApplying || isNewTool
                  ? null
                  : (
                      <SingleIcon
                        title={t("tooltip.reapply")}
                        onClick={handleReapply}
                        disabled={isApplying || !isCurrentWorkspaceSession}
                      >
                        <ReApplyIcon className="size-[14px]" />
                      </SingleIcon>
                    )}
            </>
            {(isApplying || noModifyed || isNewTool)
              ? null
              : (
                  <SingleIcon
                    title={showDiffView ? t("code.showCodeView") : t("code.showDiffView")}
                    onClick={handleToggleDiffView}
                  >
                    <DiffIconType className="size-[14px]" />
                  </SingleIcon>
                )}
          </div>
        )}
      </div>
    </div>
  );
};
