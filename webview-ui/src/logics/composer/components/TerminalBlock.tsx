import { memo, useCallback, useRef, useEffect, useState, useMemo } from "react";
import { getHighLight } from "@/components/Dialog/Highlight";
import styles from "./CodeBlockBase.module.less";
import clsx from "clsx";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { useTrustedHTML } from "@/hooks/useTrustedHTML";
import { useShikiTheme } from "@/hooks/useShikiTheme";
import { DOM } from "@/utils/dom";
import { IconCopy, IconCheck } from "@/components/Icons";
import { kwaiPilotBridgeAPI } from "@/bridge";

/*
overflowX: auto + inner div with padding results in an issue where the top/left/bottom padding renders but the right padding inside does not count as overflow as the width of the element is not exceeded. Once the inner div is outside the boundaries of the parent it counts as overflow.
https://stackoverflow.com/questions/60778406/why-is-padding-right-clipped-with-overflowscroll/77292459#77292459
this fixes the issue of right padding clipped off
"ideal" size in a given axis when given infinite available space--allows the syntax highlighter to grow to largest possible width including its padding
minWidth: "max-content",
*/

interface TerminalBlockProps {
  className?: string;
  source?: string;
  forceWrap?: boolean;
  language?: string;
  clearPrebg?: boolean;
  clearPreTopBorder?: boolean;
  canEdit?: boolean;
  onSourceChange?: (source: string) => void;
}

const TerminalBlock = memo(
  ({
    source,
    language,
    forceWrap = false,
    className,
    clearPrebg = false,
    clearPreTopBorder = false,
    canEdit = false,
    onSourceChange,
  }: TerminalBlockProps) => {
    const editorConfig = useVsEditorConfig(state => state.editorConfig);
    const theme = useShikiTheme();
    const [editableSource, setEditableSource] = useState(source || "");
    const [isCopied, setIsCopied] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const highlightRef = useRef<HTMLDivElement>(null);
    const sizeCalculationRef = useRef<HTMLDivElement>(null);

    // 更新内部状态当外部source变化时
    useEffect(() => {
      if (source !== undefined) {
        setEditableSource(source);
      }
    }, [source]);

    const currentSource = canEdit ? editableSource : source;
    // 处理用于高度计算的内容，确保空行也有高度
    const getContentForHeightCalculation = (content: string) => {
      if (!content) return " ";
      // 如果内容以换行符结尾，添加一个空格确保最后一行有高度
      if (content.endsWith("\n")) {
        return content + " ";
      }
      // 将所有空行替换为包含空格的行，确保每行都有高度
      return content.replace(/\n\n/g, "\n \n") + " ";
    };
    // 统一使用不包含pre标签的高亮HTML
    const html = useMemo(() => {
      return getHighLight({
        code: currentSource || "",
        language: language || "typescript",
        theme,
        transformers: [
          {
            pre() {
              // 始终移除pre标签，只返回其子节点
              // return node.children;
            },
            code(node) {
              node.properties.style = `background-color: transparent;font-family: ${editorConfig.fontFamily};font-size: 12px;line-height: 1.5;white-space: pre;`;
            },
            line(node) {
              node.properties.style = `margin: 0;padding: 0;white-space: pre;`;
            },
          },
        ],
      });
    }, [currentSource, language, theme, editorConfig.fontFamily]);

    const elementRef = useTrustedHTML(html);
    // 方案1: 确保光标始终可见的智能滚动 - 修复版本
    const ensureCursorVisible = useCallback(() => {
      if (!textareaRef.current) return;

      const textarea = textareaRef.current;
      const cursorPosition = textarea.selectionStart;
      const value = textarea.value;

      // 找到光标所在的行
      const lines = value.split("\n");
      let currentLineStart = 0;
      let currentLineIndex = 0;
      let cursorLinePosition = 0;

      // 计算光标在哪一行以及在该行的位置
      for (let i = 0; i < lines.length; i++) {
        const lineEnd = currentLineStart + lines[i].length;

        if (cursorPosition <= lineEnd) {
          currentLineIndex = i;
          cursorLinePosition = cursorPosition - currentLineStart;
          break;
        }

        // +1 是为了包含换行符
        currentLineStart = lineEnd + 1;
      }

      // 获取光标所在行的文本
      const currentLine = lines[currentLineIndex] || "";
      const textBeforeCursorInLine = currentLine.substring(0, cursorLinePosition);

      // 使用Canvas API精确测量当前行中光标前的文本宽度
      const canvas = DOM.createElement("canvas") as any;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const ctx = canvas.getContext("2d")!;
      ctx.font = `14px ${editorConfig.fontFamily}`;

      const textWidth = ctx.measureText(textBeforeCursorInLine).width;

      // 计算需要的滚动位置
      const containerWidth = textarea.clientWidth;
      const currentScrollLeft = textarea.scrollLeft;
      const padding = 12; // 左右padding
      const margin = 20; // 光标边距

      let newScrollLeft = currentScrollLeft;

      // 如果光标超出右边界
      if (textWidth > currentScrollLeft + containerWidth - padding - margin) {
        newScrollLeft = textWidth - containerWidth + padding + margin;
      }
      // 如果光标超出左边界
      else if (textWidth < currentScrollLeft + margin) {
        newScrollLeft = Math.max(0, textWidth - margin);
      }

      // 同步滚动所有层
      if (newScrollLeft !== currentScrollLeft) {
        textarea.scrollLeft = newScrollLeft;
        if (highlightRef.current) {
          highlightRef.current.scrollLeft = newScrollLeft;
        }
        if (sizeCalculationRef.current) {
          sizeCalculationRef.current.scrollLeft = newScrollLeft;
        }
      }
    }, [editorConfig.fontFamily]);

    // 方案3: 优化的滚动同步
    const syncScroll = useCallback((source: HTMLElement) => {
      if (!source) return;

      const scrollTop = source.scrollTop;
      const scrollLeft = source.scrollLeft;

      // 同步所有层的滚动位置
      [highlightRef.current, sizeCalculationRef.current].forEach((ref) => {
        if (ref && ref !== source) {
          ref.scrollTop = scrollTop;
          ref.scrollLeft = scrollLeft;
        }
      });

      // 确保同步是准确的
      requestAnimationFrame(() => {
        [highlightRef.current, sizeCalculationRef.current].forEach((ref) => {
          if (ref && ref !== source) {
            ref.scrollTop = scrollTop;
            ref.scrollLeft = scrollLeft;
          }
        });
      });
    }, []);

    // 改进的输入处理
    const handleTextareaChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setEditableSource(newValue);
      onSourceChange?.(newValue);

      // 延迟确保DOM更新后再调整滚动
      requestAnimationFrame(() => {
        ensureCursorVisible();
      });
    }, [onSourceChange, ensureCursorVisible]);

    // 处理滚动事件
    const handleScroll = useCallback((e: React.UIEvent<HTMLTextAreaElement>) => {
      syncScroll(e.target as HTMLTextAreaElement);
    }, [syncScroll]);
    // 处理光标移动
    const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      const navigationKeys = ["ArrowLeft", "ArrowRight", "Home", "End"];
      if (navigationKeys.includes(e.key)) {
        requestAnimationFrame(() => {
          ensureCursorVisible();
        });
      }
    }, [ensureCursorVisible]);

    // 处理鼠标点击
    const handleClick = useCallback(() => {
      requestAnimationFrame(() => {
        ensureCursorVisible();
      });
    }, [ensureCursorVisible]);

    // 复制内容到剪贴板
    const handleCopy = useCallback(async () => {
      try {
        const contentToCopy = currentSource || "";
        kwaiPilotBridgeAPI.copyToClipboard(contentToCopy);

        // 设置为已复制状态
        setIsCopied(true);

        // 3秒后恢复原状态
        setTimeout(() => {
          setIsCopied(false);
        }, 3000);
      }
      catch (error) {
        console.error("复制失败:", error);
      }
    }, [currentSource]);

    // 在内容变化时也要同步滚动
    useEffect(() => {
      if (textareaRef.current) {
        syncScroll(textareaRef.current);
      }
    }, [editableSource, syncScroll]);

    // 同步样式 - 确保textarea和高亮层完全一致
    const sharedStyles = {
      fontSize: "12px",
      lineHeight: "1.5",
      whiteSpace: "pre" as const, // 不自动换行
      // fontFamily: "var(--vscode-font-family)",
      margin: "0",
      boxSizing: "border-box" as const,
      letterSpacing: "normal",
      wordSpacing: "normal",
      textAlign: "left" as const,
      textIndent: "0",
      tabSize: 4,
      fontVariantLigatures: "none",
      fontFeatureSettings: "normal",
      wordBreak: "normal" as const,
      overflowX: "auto" as const, // 允许水平滚动
      overflowY: canEdit ? "auto" as const : "hidden" as const, // 可编辑时允许垂直滚动
      minWidth: "100%", // 确保最小宽度
    };

    // 统一的容器结构
    return (
      <div className={`px-1.5 py-1 flex items-center border-[0.6px] gap-3 bg-toolbar-hoverBackground border-transparent ${canEdit ? "cursor-pointer hover:border-textBlockQuote-border" : "cursor-text"}`}>
        <div
          style={{
            position: "relative",
            borderBottomLeftRadius: "5px",
            borderBottomRightRadius: "5px",
            flex: "1 1 0",
            minWidth: 0,
            maxHeight: canEdit ? "120px" : "none",
            overflowY: canEdit ? "auto" : (forceWrap ? "visible" : "auto"),
          }}
          className={clsx(
            styles.markdownBody,
            className,
            clearPrebg ? styles.nonePreBg : "",
            clearPreTopBorder ? styles.nonePreTopBorder : "",
          )}
        >
          {/* 隐藏的尺寸计算层 - 用于撑开容器 */}
          <div
            ref={sizeCalculationRef}
            style={{
              visibility: "hidden",
              height: "auto",
              ...sharedStyles,
            }}
          >
            {getContentForHeightCalculation(currentSource || "")}
          </div>

          {/* 高亮显示层 - 背景层，始终存在 */}
          <div
            ref={(node) => {
              if (node) {
                elementRef.current = node;
                (highlightRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
              }
            }}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              overflow: "auto",
              zIndex: 0,
              ...sharedStyles,
              // 隐藏滚动条但保持滚动功能
              scrollbarWidth: "none",
              msOverflowStyle: "none",
            }}
          />

          {/* 可编辑文本区域 - 只在canEdit为true时渲染 */}
          {canEdit && (
            <textarea
              ref={textareaRef}
              value={editableSource}
              onChange={handleTextareaChange}
              onScroll={handleScroll}
              onKeyDown={handleKeyDown}
              onClick={handleClick}
              spellCheck={false}
              style={{
                fontFamily: editorConfig.fontFamily,
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                backgroundColor: "transparent",
                border: "none",
                outline: "none",
                resize: "none",
                color: "transparent",
                caretColor: "var(--vscode-editorCursor-foreground, #000)",
                appearance: "none",
                WebkitAppearance: "none",
                MozAppearance: "textfield-multiline",
                zIndex: 2,
                ...sharedStyles,
                // 隐藏滚动条但保持滚动功能
                scrollbarWidth: "none",
                msOverflowStyle: "none",
              }}
            />
          )}
        </div>
        <div
          className="flex-shrink-0 flex cursor-pointer items-center text-icon-foreground ml-2"
          onClick={handleCopy}
          style={{ minWidth: "16px" }}
        >
          {isCopied
            ? (
                <IconCheck className="size-3" />
              )
            : (
                <IconCopy
                  className="size-3 hover:text-foreground-muted transition-colors"
                />
              )}
        </div>
      </div>
    );
  },
);

export default TerminalBlock;
