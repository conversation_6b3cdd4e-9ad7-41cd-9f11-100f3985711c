{"name": "webview-ui", "version": "1.0.2", "private": true, "scripts": {"dev": "tsc --build & VITE_CJS_IGNORE_WARNING=true vite dev", "start": "tsc --build --watch & vite build -w", "build": "tsc --build && vite build", "build:export": "vite build --config vite-export.config.ts --mode development", "build:export:prod": "vite build --config vite-export.config.ts --mode production", "build:export:watch": "vite build --config vite-export.config.ts -w", "type-check": "tsc --build && tsc --noEmit", "test": "vitest", "preview": "vite preview", "lint:style": "npx stylelint \"src/**/*.{css,less,scss,sass}\"", "lint:style:fix": "npx stylelint \"src/**/*.{css,less,scss,sass}\" --fix"}, "dependencies": {"i18next": "^25.3.0", "@chakra-ui/anatomy": "^2.2.2", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/language": "^6.10.6", "@codemirror/state": "^6.4.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortaine/fetch-event-source": "^3.0.6", "@iconify/react": "^5.2.0", "@infra-node/tee": "^0.4.0", "@ks-kwaipilot/artifact-message-parser": "workspace:*", "@ks-radar/radar": "^1.2.12", "@ks/weblogger": "^3.10.35", "@kwaipilot/markdown-render": "^0.0.4", "@lexical/react": "^0.17.1", "@lexical/text": "^0.17.1", "@lexical/utils": "^0.17.1", "@lezer/highlight": "^1.2.1", "@testing-library/react": "^16.3.0", "@tiptap/core": "^2.4.0", "@tiptap/extension-document": "^2.4.0", "@tiptap/extension-paragraph": "^2.4.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-text": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@tiptap/suggestion": "^2.4.0", "@types/lodash-es": "^4.17.12", "@types/path-browserify": "^1.0.3", "@types/unist": "^3.0.3", "@types/uuid": "^9.0.7", "@types/xterm": "^3.0.0", "@uiw/codemirror-themes": "^4.23.6", "@uiw/react-codemirror": "^4.23.6", "@yoda/bridge": "^2.0.12", "antd": "^5.20.3", "clsx": "^2.0.0", "dayjs": "^1.11.13", "diff": "^7.0.0", "framer-motion": "^10.16.12", "highlight.js": "^11.10.0", "html-to-image": "^1.11.11", "katex": "^0.16.22", "lexical": "^0.17.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mac-scrollbar": "^0.13.6", "p-timeout": "^6.1.4", "path-browserify": "^1.0.1", "re-resizable": "^6.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.13.0", "react-router-dom": "^6.26.0", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.3", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "resize-observer-polyfill": "^1.5.1", "shared": "workspace:*", "tippy.js": "^6.3.7", "uuid": "^9.0.1", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "xterm-addon-webgl": "^0.16.0", "zustand": "^4.4.7"}, "devDependencies": {"@floating-ui/react-dom": "^2.1.2", "@kid/enterprise-icon": "^1.0.543", "@tailwindcss/typography": "^0.5.10", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/mdast": "^4.0.4", "@types/nunjucks": "^3.2.6", "@types/react": "^18.2.41", "@types/react-dom": "^18.2.17", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.11", "@types/statuses": "^2.0.5", "@types/vscode-webview": "^1.57.4", "@udecode/cn": "^40.2.8", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.16", "immer": "^10.1.1", "less": "^4.2.0", "magic-string": "^0.30.17", "nunjucks": "^3.2.4", "prettier": "^2.8.8", "react-markdown": "9.0.1", "react-remark": "^2.1.0", "react-use": "^17.6.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "shiki": "^1.24.0", "statuses": "^2.0.1", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.3.5", "tailwindcss-themer": "^4.0.0", "typescript": "^5.5.4", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "use-resize-observer": "^9.1.0", "vite": "^5.3.4", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^3.0.2", "vscode-uri": "^3.1.0"}}